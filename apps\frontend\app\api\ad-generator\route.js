import { NextResponse } from 'next/server';
import { AdGenerator } from '../../../lib/adGenerator.js';

/**
 * POST /api/ad-generator - Generate ads from VIN
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { 
      vin, 
      platforms = ['facebook', 'kijiji', 'craigslist'],
      includeEmojis = true,
      bilingual = false,
      tone = 'professional',
      priceRange = null,
      additionalFeatures = []
    } = body;

    if (!vin) {
      return NextResponse.json({
        success: false,
        error: 'VIN is required'
      }, { status: 400 });
    }

    if (vin.length !== 17) {
      return NextResponse.json({
        success: false,
        error: 'VIN must be exactly 17 characters'
      }, { status: 400 });
    }

    const generator = new AdGenerator();
    const result = await generator.generateFromVIN(vin, {
      platforms,
      includeEmojis,
      bilingual,
      tone,
      priceRange,
      additionalFeatures
    });

    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      ...result,
      message: `Successfully generated ads for ${platforms.length} platform(s)`
    });

  } catch (error) {
    console.error('❌ Ad generation error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

/**
 * GET /api/ad-generator - Get available platforms and options
 */
export async function GET(request) {
  try {
    const availablePlatforms = [
      {
        id: 'facebook',
        name: 'Facebook Marketplace',
        titleLimit: 100,
        descriptionLimit: 9000,
        features: ['emojis', 'images', 'location']
      },
      {
        id: 'kijiji',
        name: 'Kijiji',
        titleLimit: 64,
        descriptionLimit: 4000,
        features: ['categories', 'contact_info']
      },
      {
        id: 'craigslist',
        name: 'Craigslist',
        titleLimit: 70,
        descriptionLimit: 8000,
        features: ['text_only', 'location']
      },
      {
        id: 'autotrader',
        name: 'AutoTrader',
        titleLimit: 80,
        descriptionLimit: 1000,
        features: ['professional', 'specs', 'dealer_info']
      }
    ];

    const toneOptions = [
      { id: 'professional', name: 'Professional', description: 'Formal, business-like tone' },
      { id: 'casual', name: 'Casual', description: 'Friendly, conversational tone' },
      { id: 'enthusiastic', name: 'Enthusiastic', description: 'Exciting, energetic tone' }
    ];

    return NextResponse.json({
      success: true,
      availablePlatforms,
      toneOptions,
      features: {
        emojis: 'Add emojis to make ads more engaging',
        bilingual: 'Generate French versions for Quebec market',
        gpt4: process.env.OPENAI_API_KEY ? 'GPT-4 powered content generation' : 'Template-based generation (add OPENAI_API_KEY for GPT-4)'
      }
    });

  } catch (error) {
    console.error('❌ Ad generator options error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
