"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> as <PERSON><PERSON> } from "hamburger-react";
import { navLinks } from "../../constants";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSidebar } from "../../contexts/SidebarContext";
import { useTheme } from "../../contexts/ThemeContext";
import { useLanguage } from "../../contexts/LanguageContext";
import { useCompare } from "../../contexts/CompareContext";
// import Image from "next/image";
import "./Navbar.css";
// import flogo from "../../assets/flogo.png";

const Logo = ({ hidden, sidebarOpen }) => (
  <div className={`logo-container ${hidden ? 'hidden' : ''} ${sidebarOpen ? 'sidebar-open' : ''}`}>
    {/* <Image
      src={flogo}
      alt="logo"
      className="logo-image"
      width={120}
      height={96}
    /> */}
    <p className="logo-text">
      <span className="logo-text-desktop">FazeNAuto</span>
    </p>
  </div>
);

const MobileNav = ({ toggle, setToggle, currentTheme }) => (
  <div className="mobile-nav-toggle">
    <Hamburger
      toggled={toggle}
      toggle={setToggle}
      size={30}
      easing="ease-in"
      duration={0.7}
      rounded
      color={currentTheme === 'dark' ? 'white' : 'black'}
    />
  </div>
);

const Navbar = () => {
  const router = useRouter();
  const { t } = useLanguage();
  const { sidebarOpen } = useSidebar();
  const [active, setActive] = useState("");
  const [toggle, setToggle] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState({});
  const [user, setUser] = useState(null);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const { currentTheme } = useTheme();

  // Function to get translated navigation titles
  const getNavTitle = (link) => {
    const titleMap = {
      'Home': 'nav.home',
      'Inventory': 'nav.inventory',
      'Financing': 'nav.financing',
      'Services': 'nav.services',
      'Used Vehicles': 'nav.used_vehicles',
      'Vehicle Information Lookup': 'nav.vehicle_info_lookup',
      'Special Offer': 'nav.special_offer',
      'Sell or Trade': 'nav.sell_or_trade',
      'Finance Department': 'nav.finance_department',
      'Financing Calculator': 'nav.financing_calculator',
      'Financing Application': 'nav.financing_application'
    };
    return t(titleMap[link.title] || link.title);
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const handleResize = () => {
        const isLarge = window.innerWidth >= 768;
        setIsLargeScreen(isLarge);
        if (isLarge) {
          setToggle(false); // Close mobile menu if resizing to desktop
        }
      };

      setIsLargeScreen(window.innerWidth >= 768);
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  // Check for authentication status
  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          // More robust validation - check if user data is valid and recent
          if (userData && userData.email && userData.role &&
              typeof userData.email === 'string' &&
              typeof userData.role === 'string' &&
              userData.email.includes('@')) {
            setUser(userData);
          } else {
            // Invalid user data, clear it
            console.log('Invalid user data found, clearing...');
            localStorage.removeItem('user');
            setUser(null);
          }
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        localStorage.removeItem('user');
        setUser(null);
      }
    };

    // Clear any invalid localStorage data on first load
    const clearInvalidData = () => {
      try {
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (key.includes('user') || key.includes('auth')) {
            const value = localStorage.getItem(key);
            if (value && value !== 'null' && value !== 'undefined') {
              try {
                const parsed = JSON.parse(value);
                if (!parsed || !parsed.email || !parsed.role) {
                  console.log(`Clearing invalid data for key: ${key}`);
                  localStorage.removeItem(key);
                }
              } catch (e) {
                console.log(`Clearing malformed data for key: ${key}`);
                localStorage.removeItem(key);
              }
            }
          }
        });
      } catch (error) {
        console.error('Error clearing invalid data:', error);
      }
    };

    clearInvalidData();
    checkAuthStatus();

    // Listen for storage changes (when user logs in from another tab or component)
    const handleStorageChange = (e) => {
      if (e.key === 'user') {
        checkAuthStatus();
      }
    };

    // Listen for custom login event
    const handleLoginEvent = () => {
      checkAuthStatus();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('userLogin', handleLoginEvent);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('userLogin', handleLoginEvent);
    };
  }, []);

  const handleDropdownToggle = (linkId) => {
    setDropdownOpen((prev) => ({
      ...prev,
      [linkId]: !prev[linkId]
    }));
    // Close user dropdown when any nav dropdown opens
    if (!dropdownOpen[linkId]) {
      setUserDropdownOpen(false);
    }
  };

  const handleLinkClick = (title) => {
    setActive(title);
    setDropdownOpen({}); // Close all dropdowns on link click
    setUserDropdownOpen(false); // Close user dropdown on link click
    setToggle(false); // Close mobile menu
  };

  const handleUserDropdownToggle = () => {
    setUserDropdownOpen((prev) => !prev);
    // Close all nav dropdowns when user dropdown opens
    if (!userDropdownOpen) {
      setDropdownOpen({});
    }
  };

  const handleLogoutClick = () => {
    setShowLogoutConfirm(true);
    setUserDropdownOpen(false);
    setToggle(false); // Close mobile menu
  };

  const confirmLogout = () => {
    localStorage.removeItem('user');
    setUser(null);
    setUserDropdownOpen(false);
    setShowLogoutConfirm(false);
    setToggle(false); // Close mobile menu
    router.push('/');
  };

  const cancelLogout = () => {
    setShowLogoutConfirm(false);
  };

  const getUserDisplayName = () => {
    if (!user) return '';
    // Extract name from email (part before @)
    const emailName = user.email.split('@')[0];
    // Handle common email <NAME_EMAIL> -> ali, <EMAIL> -> john
    const name = emailName.includes('.') ? emailName.split('.')[0] : emailName;
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Check if click is outside nav dropdowns
      if (!event.target.closest('.nav-item') && !event.target.closest('.dropdown-menu')) {
        setDropdownOpen({});
      }

      // Check if click is outside user dropdown (more specific)
      if (!event.target.closest('.admin-portal-mobile') &&
          !event.target.closest('.user-dropdown-desktop') &&
          !event.target.closest('.user-dropdown-top')) {
        setUserDropdownOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  return (
    <nav className={`navbar ${isLargeScreen ? "navbar-large" : "navbar-small"}`}>
      <div className="navbar-container">
        {/* Logo - Always visible */}
        <div className="navbar-left">
          <Link href="/" onClick={() => handleLinkClick("")}>
            <Logo hidden={false} sidebarOpen={sidebarOpen} />
          </Link>
        </div>

        {/* Admin Portal User Section - Mobile Top Bar (moved next to hamburger) */}
        {!isLargeScreen && user && (
          <div className="admin-portal-mobile">
            <div className="user-dropdown-mobile-top">
              <a
                className="user-link-mobile-top"
                onClick={handleUserDropdownToggle}
              >
                <span className="user-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                  </svg>
                </span>
                <span className="mobile-dropdown-arrow">{userDropdownOpen ? "▲" : "▼"}</span>
              </a>
              <ul className={`dropdown-menu user-dropdown-top ${userDropdownOpen ? "show" : ""}`}>
                <li className="dropdown-item">
                  <Link href="/admin" onClick={() => handleLinkClick('Dashboard')}>
                    {t('nav.dashboard')}
                  </Link>
                </li>
                <li className="dropdown-item">
                  <Link href="/admin/vehicle-info" onClick={() => handleLinkClick('Vehicle Scan')}>
                    {t('nav.vehicle_scan')}
                  </Link>
                </li>
                <li className="dropdown-item">
                  <Link href="/admin/vehicles/upload" onClick={() => handleLinkClick('Upload New')}>
                    {t('nav.upload_new')}
                  </Link>
                </li>
                <li className="dropdown-item">
                  <Link href="/admin/compliance-forms" onClick={() => handleLinkClick('Compliance Forms')}>
                    {t('nav.compliance_forms')}
                  </Link>
                </li>

                <li className="dropdown-item">
                  <Link href="/admin/profile" onClick={() => handleLinkClick('User Profile Settings')}>
                    {t('nav.user_profile')}
                  </Link>
                </li>
                <li className="dropdown-item">
                  <a onClick={handleLogoutClick} className="logout-link">
                    {t('nav.logout')}
                  </a>
                </li>
              </ul>
            </div>
          </div>
        )}

        {/* Only show hamburger menu for non-logged-in users */}
        {!user && <MobileNav toggle={toggle} setToggle={setToggle} currentTheme={currentTheme} />}

        {/* Mobile Menu - Only show for non-logged-in users */}
        {!user && <div className={`mobile-menu ${toggle ? "show" : "hide"}`}>
          <ul className="nav-list mobile-list">
            {navLinks.filter(link => link.id !== 'login').map((link) => (
              <li key={link.id} className={`nav-item ${link.subLinks && dropdownOpen[link.id] ? 'dropdown-open' : ''}`}>
                {link.subLinks ? (
                  <>
                    <a
                      className={`nav-link ${active === link.title ? "active" : ""}`}
                      onClick={() => {
                        setActive(link.title);
                        handleDropdownToggle(link.id);
                      }}
                    >
                      {getNavTitle(link)} <span>{dropdownOpen[link.id] ? "▲" : "▼"}</span>
                    </a>
                    <ul className={`dropdown-menu ${dropdownOpen[link.id] ? "show" : ""}`}>
                      {link.subLinks.map((sub) => (
                        <li key={sub.id} className="dropdown-item">
                          <Link
                            href={`/${sub.id}`}
                            onClick={() => handleLinkClick(sub.title)}
                          >
                            {getNavTitle(sub)}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </>
                ) : (
                  <Link
                    className={`nav-link ${active === link.title ? "active" : ""}`}
                    href={link.id === "home" ? "/" : `/${link.id}`}
                    onClick={() => handleLinkClick(link.title)}
                  >
                    {getNavTitle(link)}
                  </Link>
                )}
              </li>
            ))}

            {/* Mobile Login Section - Only show if not authenticated */}
            {!user && (
              <li className="nav-item mobile-login">
                <Link
                  className={`nav-link ${active === "Dealer Login" ? "active" : ""}`}
                  href="/login"
                  onClick={() => handleLinkClick("Dealer Login")}
                >
                  {t('nav.login')}
                </Link>
              </li>
            )}
          </ul>
        </div>}

        {/* Desktop Menu */}
        <ul className="nav-list desktop-list">
          {navLinks.filter(link => link.id !== 'login').map((link) => (
            <li key={link.id} className="nav-item">
              {link.subLinks ? (
                <>
                  <a
                    className={`nav-link ${active === link.title ? "active" : ""}`}
                    onClick={() => {
                      setActive(link.title);
                      handleDropdownToggle(link.id);
                    }}
                  >
                    {getNavTitle(link)} <span className="dropdown-arrow">{dropdownOpen[link.id] ? "▲" : "▼"}</span>
                  </a>
                  <ul className={`dropdown-menu ${dropdownOpen[link.id] ? "show" : ""}`}>
                    {link.subLinks.map((sub) => (
                      <li key={sub.id} className="dropdown-item">
                        <Link
                          href={`/${sub.id}`}
                          onClick={() => handleLinkClick(sub.title)}
                        >
                          {getNavTitle(sub)}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </>
              ) : (
                <Link
                  className={`nav-link ${active === link.title ? "active" : ""}`}
                  href={link.id === "home" ? "/" : `/${link.id}`}
                  onClick={() => handleLinkClick(link.title)}
                >
                  {getNavTitle(link)}
                </Link>
              )}
            </li>
          ))}

          {/* User Authentication Section - Desktop */}
          <li className="nav-item user-auth-desktop">
            {user ? (
              <div className="user-dropdown-desktop">
                <a
                  className="nav-link user-link"
                  onClick={handleUserDropdownToggle}
                >
                  <span className="user-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                  </span>
                  {getUserDisplayName()}
                  <span className="dropdown-arrow">{userDropdownOpen ? "▲" : "▼"}</span>
                </a>
                <ul className={`dropdown-menu user-dropdown ${userDropdownOpen ? "show" : ""}`}>
                  <li className="dropdown-item">
                    <Link href="/admin" onClick={() => handleLinkClick('Dashboard')}>
                      {t('nav.dashboard')}
                    </Link>
                  </li>
                  <li className="dropdown-item">
                    <Link href="/admin/vehicle-info" onClick={() => handleLinkClick('Vehicle Scan')}>
                      {t('nav.vehicle_scan')}
                    </Link>
                  </li>
                  <li className="dropdown-item">
                    <Link href="/admin/vehicles/upload" onClick={() => handleLinkClick('Upload New')}>
                      {t('nav.upload_new')}
                    </Link>
                  </li>
                  <li className="dropdown-item">
                    <Link href="/admin/compliance-forms" onClick={() => handleLinkClick('Compliance Forms')}>
                      {t('nav.compliance_forms')}
                    </Link>
                  </li>

                  <li className="dropdown-item">
                    <Link href="/admin/profile" onClick={() => handleLinkClick('User Profile Settings')}>
                      {t('nav.user_profile')}
                    </Link>
                  </li>
                  <li className="dropdown-item">
                    <a onClick={handleLogoutClick} className="logout-link">
                      {t('nav.logout')}
                    </a>
                  </li>
                </ul>
              </div>
            ) : (
              <div className="user-dropdown-desktop">
                <a
                  className="nav-link"
                  onClick={handleUserDropdownToggle}
                >
                  {t('nav.login')}
                  <span className="dropdown-arrow">{userDropdownOpen ? "▲" : "▼"}</span>
                </a>
                <ul className={`dropdown-menu user-dropdown ${userDropdownOpen ? "show" : ""}`}>
                  <li className="dropdown-item">
                    <Link href="/login" onClick={() => handleLinkClick('Login')}>
                      {t('nav.login')}
                    </Link>
                  </li>
                </ul>
              </div>
            )}
          </li>
        </ul>
      </div>

      {/* Logout Confirmation Modal */}
      {showLogoutConfirm && (
        <div className="logout-modal-overlay">
          <div className="logout-modal">
            <h3>Are you sure you want to log out?</h3>
            <div className="logout-modal-buttons">
              <button onClick={confirmLogout} className="logout-confirm-btn">
                Yes
              </button>
              <button onClick={cancelLogout} className="logout-cancel-btn">
                No
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
