/* Vehicle Details Page Styling */

.container {
  min-height: 100vh;
  background: var(--bg-secondary, #f8f9fa);
  padding: 2rem 1rem;
  transition: background 0.3s ease;
}

/* Dark mode: Apply consistent gradient background */
[data-theme="dark"] .container {
  background: var(--bg-primary);
}

.header {
  max-width: 1200px;
  margin: 0 auto 2rem auto;
}

.backBtn {
  background: var(--accent-primary, #4299e1);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.backBtn::before {
  content: '←';
  font-size: 1.1rem;
  transition: transform 0.3s ease;
  margin-right: 0.25rem;
}

.backBtn:hover::before {
  transform: translateX(-4px);
  animation: arrowBounce 0.6s ease-in-out;
}

@keyframes arrowBounce {
  0%, 100% { transform: translateX(-4px); }
  50% { transform: translateX(-8px); }
}

.backBtn:hover {
  background: var(--accent-hover, #3182ce);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.vehicleDetails {
  max-width: 1600px; /* Further increased overall card size */
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1.8fr 0.8fr; /* Left side even larger, right side smaller */
  gap: 2.5rem; /* Increased gap for better spacing */
  background: var(--bg-primary, white);
  border-radius: 12px;
  box-shadow: var(--card-shadow-hover, 0 4px 20px rgba(0, 0, 0, 0.1));
  overflow: hidden;
  min-height: 800px; /* Increased minimum height */
}

/* Better responsive grid layout */
@media (max-width: 1024px) {
  .vehicleDetails {
    grid-template-columns: 1fr;
    gap: 0;
    max-width: 100%;
    margin: 0 1rem;
    display: flex;
    flex-direction: column;
  }
}

/* Medium screens (768px - 1024px) - Follow mobile layout order */
@media (max-width: 1024px) and (min-width: 768px) {
  /* Layout Order:
     1. Title Section (title, price, tax, est payment)
     2. Image Section
     3. Description Section
     4. Vehicle Details Section
     5. Features & Options Section
     6. Action Buttons
     7. Disclaimer
  */

  /* 1. Title Section - Show only title and pricing info */
  .titleSection {
    order: 1;
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    background: var(--bg-primary, white);
    border-radius: 12px 12px 0 0;
    margin: 0 1rem;
  }

  /* 2. Image Section */
  .imageSection {
    order: 2;
    margin: 0 1rem;
  }

  /* Hide features section from image area on medium screens */
  .imageSection .enhancedFeaturesSection {
    display: none;
  }

  /* 3. Description Section */
  .descriptionSection {
    order: 3;
    padding: 1.5rem;
    margin: 1rem;
    background: var(--bg-primary, white);
    border-radius: 12px;
    border: 1px solid var(--border-primary, #e2e8f0);
  }

  /* 4. Vehicle Details Section */
  .detailsSection {
    order: 4;
    padding: 1.5rem;
    margin: 1rem;
    background: var(--bg-primary, white);
    border-radius: 12px;
    border: 1px solid var(--border-primary, #e2e8f0);
  }

  /* 5. Features Section positioned after details */
  .mobileFeatures {
    order: 5;
    display: block;
    margin: 0 1rem 1rem 1rem;
  }

  /* 6. Action Buttons */
  .actionButtons {
    order: 6;
    flex-direction: column;
    padding: 0 1.5rem;
    margin: 0 1rem 1rem 1rem;
  }

  /* 7. Disclaimer */
  .disclaimer {
    order: 7;
    padding: 0 1.5rem 1.5rem 1.5rem;
    margin: 0 1rem;
  }

  /* Hide the main info section container styling on medium screens */
  .infoSection {
    display: contents; /* This makes the container invisible but keeps children */
  }

  /* Dark mode styling for medium screen sections */
  [data-theme="dark"] .titleSection {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  [data-theme="dark"] .descriptionSection {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  [data-theme="dark"] .detailsSection {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  [data-theme="dark"] .mobileFeatures {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

.imageSection {
  position: relative;
}

.mainImageContainer {
  position: relative;
  margin-bottom: 1rem;
}

.mainImage {
  width: 100%;
  height: 400px;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 12px 0 0 12px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.mainImage:hover {
  transform: scale(1.02);
}

/* Improved responsive image scaling */
@media (max-width: 1024px) {
  .mainImage {
    border-radius: 12px 12px 0 0;
    height: 350px;
  }
}

@media (min-width: 1024px) {
  .mainImage {
    height: 450px;
  }
}

@media (min-width: 1200px) {
  .mainImage {
    height: 500px;
  }
}

@media (min-width: 1440px) {
  .mainImage {
    height: 550px;
  }
}

.prevBtn, .nextBtn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 2;
  opacity: 0; /* Hidden by default */
  visibility: hidden;
}

/* Show arrows only on hover or touch */
.mainImageContainer:hover .prevBtn,
.mainImageContainer:hover .nextBtn,
.mainImageContainer.touched .prevBtn,
.mainImageContainer.touched .nextBtn {
  opacity: 1;
  visibility: visible;
}

.prevBtn:hover, .nextBtn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-50%) scale(1.1);
}

.prevBtn {
  left: 15px;
}

.nextBtn {
  right: 15px;
}

.thumbnailGallery {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  padding: 0.5rem 0;
  scrollbar-width: thin;
}

.thumbnailGallery::-webkit-scrollbar {
  height: 4px;
}

.thumbnailGallery::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.thumbnailGallery::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 2px;
}

.thumbnailWrapper {
  position: relative;
  width: 80px;
  height: 60px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  flex-shrink: 0;
  overflow: hidden;
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  display: block;
}

.thumbnailWrapper:hover {
  transform: scale(1.05);
  border-color: #4299e1;
}

.activeThumbnail {
  border-color: #4299e1;
  transform: scale(1.05);
}

/* Video indicator overlay */
.videoIndicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 4px;
  pointer-events: none;
}

.playIcon {
  color: white;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.infoSection {
  padding: 2rem; /* Increased padding for expanded card */
  background: var(--bg-primary);
  color: var(--text-primary);
  max-width: 100%; /* Ensure it doesn't exceed container */
  display: flex;
  flex-direction: column;
  height: 100%; /* Take full height of the grid cell */
}

.titleSection {
  margin-bottom: 1.5rem; /* Reduced margin */
  padding-bottom: 1rem; /* Reduced padding */
  border-bottom: 2px solid var(--border-primary, #e2e8f0);
}

.vehicleTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.priceSection {
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.price {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
}

.taxText {
  font-size: 1rem;
  font-weight: 400;
  color: var(--text-primary, #718096);
}

.estimatedPayment {
  font-size: 1rem;
  color: var(--text-primary, #718096);
  margin-bottom: 1rem;
}

.specsSection {
  margin-bottom: 2rem;
}

.specRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-primary, #e2e8f0);
  transition: background-color 0.2s ease;
}

.specRow:hover {
  background-color: var(--bg-tertiary, #f7fafc);
  margin: 0 -1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  border-radius: 6px;
}

.specRow:last-child {
  border-bottom: none;
}

.specLabel {
  font-weight: 600;
  color: var(--text-primary, #4a5568);
  font-size: 0.95rem;
}

.specValue {
  font-weight: 500;
  color: var(--text-primary, #2d3748);
  font-size: 0.95rem;
}

.detailsSection {
  margin-bottom: 1.5rem; /* Reduced margin */
}

.detailsSection h2 {
  font-size: 1.3rem; /* Slightly smaller heading */
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 0.75rem; /* Reduced margin */
}

.detailsList {
  display: grid;
  gap: 0.5rem; /* Reduced gap for more compact layout */
}

.detailItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0; /* Reduced padding */
  border-bottom: 1px solid #e2e8f0;
}

.detailItem:last-child {
  border-bottom: none;
}

.detailLabel {
  font-weight: 600;
  color: var(--text-primary, #4a5568);
}

.detailValue {
  font-weight: 500;
  color: var(--text-primary, #2d3748);
}

/* Description Section Styles */
.descriptionSection {
  background: var(--bg-primary, rgba(255, 255, 255, 0.9));
  border-radius: 16px;
  border: 1px solid var(--border-primary, rgba(226, 232, 240, 0.6));
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  margin-top: 3rem; /* Push description lower on desktop */
}

.descriptionTitle {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
}

.descriptionText {
  color: var(--text-primary, #4a5568);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* Mobile Description Section */
.mobileDescription {
  background: var(--bg-primary, rgba(255, 255, 255, 0.9));
  border-radius: 16px;
  border: 1px solid var(--border-primary, rgba(226, 232, 240, 0.6));
  padding: 1.5rem;
  margin: 0 1rem 1rem 1rem;
  order: 3.5; /* Position between details and features */
}

/* Dark mode description styling */
[data-theme="dark"] .descriptionSection {
  background: rgba(13, 17, 23, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .mobileDescription {
  background: rgba(13, 17, 23, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .descriptionTitle {
  color: var(--text-primary, #ffffff);
}

[data-theme="dark"] .descriptionText {
  color: var(--text-primary, #e2e8f0);
}

.actionButtons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem; /* Reduced margin */
}

.contactBtn {
  flex: 1;
  background: #48bb78;
  color: white;
  border: none;
  padding: 0.75rem 1.25rem; /* Slightly reduced padding */
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.95rem; /* Slightly smaller font */
}

.contactBtn:hover {
  background: #38a169;
}

.financeBtn {
  flex: 1;
  background: #4299e1;
  color: white;
  border: none;
  padding: 0.75rem 1.25rem; /* Slightly reduced padding */
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.95rem; /* Slightly smaller font */
}

.financeBtn:hover {
  background: #3182ce;
}

.disclaimer {
  background: #fef5e7;
  border: 1px solid #f6e05e;
  border-radius: 6px;
  padding: 1rem;
  color: #744210;
  font-size: 0.9rem;
}

/* Image Modal Styles */
.imageModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modalContent {
  position: relative;
  max-width: 95vw;
  max-height: 95vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalImage {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  min-width: 80vw;
  min-height: 60vh;
}

.closeModal {
  position: absolute;
  top: -50px;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.closeModal:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modalPrevBtn, .modalNextBtn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modalPrevBtn:hover, .modalNextBtn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.modalPrevBtn {
  left: -80px;
}

.modalNextBtn {
  right: -80px;
}

/* Loading State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #4299e1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  background: white;
  border-radius: 8px;
  padding: 3rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error h2 {
  color: #e53e3e;
  margin-bottom: 1rem;
}

.error p {
  color: #4a5568;
  margin-bottom: 2rem;
}

/* SOLD Badge */
.soldBadge {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
  z-index: 10;
}

.soldText {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white;
  font-size: 1.6rem;
  font-weight: 900;
  padding: 0.6rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  display: inline-block;
  transform: rotate(-5deg);
  border: 3px solid white;
  position: relative;
}

.soldText::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  border-radius: 8px;
  z-index: -1;
  filter: blur(6px);
  opacity: 0.6;
}

.soldText::after {
  content: '★';
  position: absolute;
  top: -10px;
  right: -10px;
  color: #fbbf24;
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* ON HOLD Badge */
.onHoldBadge {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
  z-index: 10;
}

.onHoldText {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  font-size: 1.6rem;
  font-weight: 900;
  padding: 0.6rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  display: inline-block;
  transform: rotate(-5deg);
  border: 3px solid white;
  position: relative;
}

.onHoldText::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-radius: 8px;
  z-index: -1;
  filter: blur(6px);
  opacity: 0.6;
}

.onHoldText::after {
  content: '⏸';
  position: absolute;
  top: -10px;
  right: -10px;
  color: #fbbf24;
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Features Section */
.featuresSection {
  margin-top: 2rem;
  padding: 1.5rem;
  background: var(--bg-tertiary, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border-primary, #e9ecef);
}

.featuresTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1.5rem;
  text-align: center;
  border-bottom: 2px solid var(--border-primary, #e2e8f0);
  padding-bottom: 0.75rem;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.featureCategory {
  background: var(--bg-primary, white);
  border-radius: 8px;
  padding: 1.25rem;
  box-shadow: var(--card-shadow, 0 2px 8px rgba(0, 0, 0, 0.1));
  border: 1px solid var(--border-primary, #e2e8f0);
}

.categoryTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-primary, #e2e8f0);
  text-align: center;
}

.featuresList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 6px;
  background: var(--bg-tertiary, #f7fafc);
  border: 1px solid var(--border-primary, #e2e8f0);
  transition: all 0.3s ease;
  justify-content: space-between;
}



/* Dark mode specific feature item styling */
[data-theme="dark"] .featureItem.included {
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

[data-theme="dark"] .featureItem.not_available {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

/* Light mode feature item styling */
[data-theme="light"] .featureItem.included,
:root .featureItem.included {
  background: #f0fff4;
  border-color: #9ae6b4;
}

[data-theme="light"] .featureItem.not_available,
:root .featureItem.not_available {
  background: #fff5f5;
  border-color: #feb2b2;
}

.featureIcon {
  font-weight: bold;
  font-size: 1rem;
  min-width: 20px;
}

.featureText {
  flex: 1;
  color: var(--text-primary, #2d3748);
  font-weight: 500;
}

/* Dark mode feature text styling */
[data-theme="dark"] .featureText {
  color: #ffffff;
}

.featureStatus {
  font-size: 0.85rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.05);
}

/* Dark mode feature status styling */
[data-theme="dark"] .featureItem.included .featureStatus {
  color: #10b981;
  background: rgba(16, 185, 129, 0.2);
}

[data-theme="dark"] .featureItem.not_available .featureStatus {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.2);
}

/* Light mode feature status styling */
[data-theme="light"] .featureItem.included .featureStatus,
:root .featureItem.included .featureStatus {
  color: #22543d;
  background: #c6f6d5;
}

[data-theme="light"] .featureItem.not_available .featureStatus,
:root .featureItem.not_available .featureStatus {
  color: #742a2a;
  background: #fed7d7;
}

/* Video Section */
.videoSection {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(233, 236, 239, 0.6);
  backdrop-filter: blur(10px);
}

.videoTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  text-align: center;
}

.vehicleVideo {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #000;
}

.vehicleVideo:focus {
  outline: 2px solid #4299e1;
  outline-offset: 2px;
}

/* Enhanced Features Section */
.enhancedFeaturesSection {
  margin-top: 3rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Hide mobile features on desktop */
.mobileFeatures {
  display: none;
}

/* Dark mode enhanced features section */
[data-theme="dark"] .enhancedFeaturesSection {
  background: rgba(13, 17, 23, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.enhancedFeaturesTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1.5rem;
  text-align: left;
  border-bottom: 2px solid var(--border-primary, rgba(226, 232, 240, 0.8));
  padding-bottom: 0.75rem;
}

.keyFeatures {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.keyFeatureItem {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--bg-tertiary, rgba(248, 249, 250, 0.8));
  border-radius: 8px;
  border: 1px solid var(--border-primary, rgba(226, 232, 240, 0.6));
  transition: all 0.3s ease;
}



.keyFeatureName {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary, #4a5568);
}

.viewAllBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--bg-primary, rgba(255, 255, 255, 0.9));
  border: 1px solid var(--border-primary, rgba(226, 232, 240, 0.8));
  color: var(--text-primary, #4a5568);
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;
  backdrop-filter: blur(5px);
}

.viewAllBtn:hover {
  background: rgba(66, 153, 225, 0.1);
  border-color: rgba(66, 153, 225, 0.3);
  color: var(--text-primary, #2d3748);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(66, 153, 225, 0.2);
}

.fullFeaturesSection {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
}

.fullFeaturesSection .featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.fullFeaturesSection .featureCategory {
  background: var(--bg-tertiary, rgba(248, 249, 250, 0.8));
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid var(--border-primary, rgba(226, 232, 240, 0.6));
  backdrop-filter: blur(5px);
}

.fullFeaturesSection .categoryTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary, #4a5568);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-primary, rgba(226, 232, 240, 0.8));
}

.categoryFeatures {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fullFeaturesSection .featureItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--bg-primary, rgba(255, 255, 255, 0.8));
  border-radius: 8px;
  border: 1px solid var(--border-primary, rgba(226, 232, 240, 0.4));
  transition: all 0.2s ease;
}



.fullFeaturesSection .featureIcon {
  font-size: 1rem;
  min-width: 20px;
  text-align: center;
}

/* Dark mode feature icons */
[data-theme="dark"] .fullFeaturesSection .featureItem.included .featureIcon {
  color: #10b981;
}

[data-theme="dark"] .fullFeaturesSection .featureItem.not_available .featureIcon {
  color: #ef4444;
}

/* Light mode feature icons */
[data-theme="light"] .fullFeaturesSection .featureItem.included .featureIcon,
:root .fullFeaturesSection .featureItem.included .featureIcon {
  color: #38a169;
}

[data-theme="light"] .fullFeaturesSection .featureItem.not_available .featureIcon,
:root .fullFeaturesSection .featureItem.not_available .featureIcon {
  color: #e53e3e;
}

.fullFeaturesSection .featureText {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary, #4a5568);
}

/* Dark mode full features text styling */
[data-theme="dark"] .fullFeaturesSection .featureText {
  color: #ffffff;
}

.fullFeaturesSection .featureStatus {
  font-size: 0.8rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Dark mode full features status */
[data-theme="dark"] .fullFeaturesSection .featureItem.included .featureStatus {
  color: #10b981;
  background: rgba(16, 185, 129, 0.2);
}

[data-theme="dark"] .fullFeaturesSection .featureItem.not_available .featureStatus {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.2);
}

/* Light mode full features status */
[data-theme="light"] .fullFeaturesSection .featureItem.included .featureStatus,
:root .fullFeaturesSection .featureItem.included .featureStatus {
  color: #22543d;
  background: rgba(198, 246, 213, 0.8);
}

[data-theme="light"] .fullFeaturesSection .featureItem.not_available .featureStatus,
:root .fullFeaturesSection .featureItem.not_available .featureStatus {
  color: #742a2a;
  background: rgba(254, 215, 215, 0.8);
}

/* Responsive Design for Enhanced Features */
@media (max-width: 768px) {
  .enhancedFeaturesSection {
    margin-top: 2rem;
    padding: 1.5rem;
  }

  .enhancedFeaturesTitle {
    font-size: 1.3rem;
  }

  .keyFeatures {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .keyFeatureItem {
    padding: 0.5rem 0.75rem;
  }

  .keyFeatureName {
    font-size: 0.85rem;
  }

  .viewAllBtn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }

  .fullFeaturesSection .featuresGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .fullFeaturesSection .featureCategory {
    padding: 1rem;
  }

  .fullFeaturesSection .categoryTitle {
    font-size: 1rem;
  }

  .fullFeaturesSection .featureItem {
    padding: 0.5rem;
    gap: 0.5rem;
  }

  .fullFeaturesSection .featureText {
    font-size: 0.85rem;
  }

  .fullFeaturesSection .featureStatus {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
  }
}

@media (max-width: 480px) {
  .enhancedFeaturesSection {
    padding: 1rem;
    margin-top: 1.5rem;
  }

  .enhancedFeaturesTitle {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .keyFeatures {
    gap: 0.4rem;
  }

  .keyFeatureItem {
    padding: 0.4rem 0.6rem;
  }

  .keyFeatureName {
    font-size: 0.8rem;
  }

  .viewAllBtn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .fullFeaturesSection .featureCategory {
    padding: 0.75rem;
  }

  .fullFeaturesSection .categoryTitle {
    font-size: 0.95rem;
    margin-bottom: 0.75rem;
  }

  .fullFeaturesSection .featureItem {
    padding: 0.4rem;
    gap: 0.4rem;
  }

  .fullFeaturesSection .featureText {
    font-size: 0.8rem;
  }

  .fullFeaturesSection .featureIcon {
    font-size: 0.85rem;
    min-width: 14px;
  }

  .fullFeaturesSection .featureStatus {
    font-size: 0.7rem;
    padding: 0.15rem 0.3rem;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .vehicleDetails {
    grid-template-columns: 1fr;
    gap: 0;
    display: flex;
    flex-direction: column;
  }

  /* Mobile Layout Order:
     1. Title Section (title, price, tax, est payment)
     2. Image Section
     3. Description Section
     4. Vehicle Details Section
     5. Features & Options Section (mobile version)
     6. Action Buttons
     7. Disclaimer
  */

  /* 1. Title Section - Show only title and pricing info */
  .titleSection {
    order: 1;
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    background: var(--bg-primary, white);
    border-radius: 12px 12px 0 0;
    margin: 0 1rem;
  }

  /* 2. Image Section */
  .imageSection {
    order: 2;
    margin: 0 1rem;
  }

  /* Hide features section from image area on mobile */
  .imageSection .enhancedFeaturesSection {
    display: none;
  }

  /* 3. Description Section */
  .descriptionSection {
    order: 3;
    padding: 1.5rem;
    margin: 1rem;
    background: var(--bg-primary, white);
    border-radius: 12px;
    border: 1px solid var(--border-primary, #e2e8f0);
  }

  /* 4. Vehicle Details Section */
  .detailsSection {
    order: 4;
    padding: 1.5rem;
    margin: 1rem;
    background: var(--bg-primary, white);
    border-radius: 12px;
    border: 1px solid var(--border-primary, #e2e8f0);
  }

  /* 5. Mobile Features Section */
  .mobileFeatures {
    order: 5;
    display: block;
    margin: 0 1rem 1rem 1rem;
  }

  /* 6. Action Buttons */
  .actionButtons {
    order: 6;
    flex-direction: column;
    padding: 0 1.5rem;
    margin: 0 1rem 1rem 1rem;
  }

  /* 7. Disclaimer */
  .disclaimer {
    order: 7;
    padding: 0 1.5rem 1.5rem 1.5rem;
    margin: 0 1rem;
  }

  /* Hide the main info section container styling on mobile */
  .infoSection {
    display: contents; /* This makes the container invisible but keeps children */
  }

  .mainImage {
    height: 300px;
    border-radius: 12px;
  }

  .vehicleTitle {
    font-size: 2rem;
  }

  .price {
    font-size: 1.75rem;
  }

  .modalPrevBtn {
    left: -60px;
  }

  .modalNextBtn {
    right: -60px;
  }

  .modalPrevBtn, .modalNextBtn {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }

  /* Mobile Features & Options styling */
  .mobileFeatures {
    background: var(--bg-primary, rgba(255, 255, 255, 0.9));
    border-radius: 16px;
    border: 1px solid var(--border-primary, rgba(226, 232, 240, 0.6));
    padding: 2rem;
  }

  /* Dark mode mobile features styling */
  [data-theme="dark"] .mobileFeatures {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Dark mode styling for mobile sections */
  [data-theme="dark"] .titleSection {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  [data-theme="dark"] .descriptionSection {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  [data-theme="dark"] .detailsSection {
    background: rgba(13, 17, 23, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .infoSection {
    padding: 1rem;
  }

  .vehicleTitle {
    font-size: 1.75rem;
  }

  .prevBtn, .nextBtn {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .prevBtn {
    left: 10px;
  }

  .nextBtn {
    right: 10px;
  }

  .modalPrevBtn, .modalNextBtn {
    position: relative;
    left: auto;
    right: auto;
    margin: 0 10px;
    width: 45px;
    height: 45px;
    font-size: 20px;
  }

  .modalContent {
    flex-direction: column;
  }

  .closeModal {
    top: -40px;
    right: 10px;
    width: 35px;
    height: 35px;
    font-size: 20px;
  }
}
