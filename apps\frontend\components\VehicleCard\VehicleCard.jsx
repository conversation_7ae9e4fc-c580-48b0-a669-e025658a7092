import { useCompare } from '../../contexts/CompareContext';
import styles from './VehicleCard.module.css';

export default function VehicleCard({ vehicle, showCompareButton = false, showAdminActions = false }) {
  const { addToCompare, removeFromCompare, isInCompare, canAddMore } = useCompare();

  // Get the first image from images array or fall back to imageUrl
  const primaryImage = (vehicle.images && vehicle.images.length > 0)
    ? vehicle.images[0]
    : vehicle.imageUrl;

  const vehicleId = vehicle._id || vehicle.id;
  const isInComparison = isInCompare(vehicleId);

  const handleCompareClick = () => {
    if (isInComparison) {
      removeFromCompare(vehicleId);
    } else if (canAddMore) {
      addToCompare(vehicleId);
    }
  };

  return (
    <div className={styles.vehicleCard}>
      <div className={styles.imageWrapper}>
        <img
          src={primaryImage}
          alt={`${vehicle.year} ${vehicle.make} ${vehicle.model}`}
          className={styles.vehicleImage}
        />
        <span className={styles.priceTag}>${vehicle.price.toLocaleString()}</span>

        {/* Status Banners */}
        {vehicle.status === 'sold' && (
          <div className={`${styles.statusBanner} ${styles.sold}`}>
            SOLD
          </div>
        )}
        {vehicle.status === 'on_hold' && (
          <div className={`${styles.statusBanner} ${styles.onHold}`}>
            ON HOLD
          </div>
        )}

        {/* Compare Button - positioned in top right of image */}
        {showCompareButton && (
          <button
            className={`${styles.compareBtn} ${isInComparison ? styles.compareBtnActive : ''} ${!canAddMore && !isInComparison ? styles.compareBtnDisabled : ''}`}
            onClick={handleCompareClick}
            disabled={!canAddMore && !isInComparison}
            title={isInComparison ? 'Remove from comparison' : canAddMore ? 'Add to comparison' : 'Maximum 4 vehicles can be compared'}
          >
            {isInComparison ? '✓' : '+'}
          </button>
        )}
      </div>
      <div className={styles.vehicleInfo}>
        <h3>{vehicle.year} {vehicle.make} {vehicle.model}</h3>
        <p>Color: {vehicle.color}</p>
        <p>Odometer: {vehicle.mileage.toLocaleString()} km</p>
        <p>Engine: {vehicle.engine ? vehicle.engine.charAt(0).toUpperCase() + vehicle.engine.slice(1) : 'N/A'}</p>
        <p>Transmission: {vehicle.transmission ? vehicle.transmission.charAt(0).toUpperCase() + vehicle.transmission.slice(1) : 'N/A'}</p>
        <p>Fuel Type: {vehicle.fuelType ? vehicle.fuelType.charAt(0).toUpperCase() + vehicle.fuelType.slice(1) : 'N/A'}</p>
        <p>Doors: {vehicle.doors || 'N/A'}</p>
        <p>Drivetrain: {vehicle.drivetrain ? vehicle.drivetrain.charAt(0).toUpperCase() + vehicle.drivetrain.slice(1) : 'N/A'}</p>

        {showAdminActions && (
          <div className={styles.cardActions}>
            <button className={styles.editBtn}>Edit</button>
            <button className={styles.deleteBtn}>Delete</button>
          </div>
        )}
      </div>
    </div>
  );
}
