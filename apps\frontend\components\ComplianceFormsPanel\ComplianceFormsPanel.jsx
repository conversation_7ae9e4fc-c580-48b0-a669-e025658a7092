'use client';

import { useState, useEffect } from 'react';
import styles from './ComplianceFormsPanel.module.css';
import ValidationModal from '../ValidationModal/ValidationModal';

export default function ComplianceFormsPanel({ selectedVehicles = [], onRefresh, onFormGenerated }) {
  const [selectedForm, setSelectedForm] = useState('');
  const [formData, setFormData] = useState({});
  const [generating, setGenerating] = useState(false);
  const [results, setResults] = useState(null);
  const [vehicles, setVehicles] = useState([]);
  const [loadingVehicles, setLoadingVehicles] = useState(false);
  const [selectedVehicleForForm, setSelectedVehicleForForm] = useState('');
  const [rins, setRins] = useState([]);
  const [loadingRins, setLoadingRins] = useState(false);
  const [selectedRinForForm, setSelectedRinForForm] = useState('');
  const [manualVehicleEntry, setManualVehicleEntry] = useState(false);
  const [validationModal, setValidationModal] = useState({
    isOpen: false,
    errors: []
  });

  // Fetch vehicles and RINs on component mount
  useEffect(() => {
    fetchVehicles();
    fetchRins();
  }, []);

  const fetchVehicles = async () => {
    setLoadingVehicles(true);
    try {
      const response = await fetch('/api/vehicles');
      if (response.ok) {
        const data = await response.json();
        setVehicles(data.data || []); // Fixed: API returns data.data, not data.vehicles
      }
    } catch (error) {
      console.error('Failed to fetch vehicles:', error);
    } finally {
      setLoadingVehicles(false);
    }
  };

  const fetchRins = async () => {
    setLoadingRins(true);
    try {
      const response = await fetch('/api/rins');
      if (response.ok) {
        const data = await response.json();
        setRins(data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch RINs:', error);
    } finally {
      setLoadingRins(false);
    }
  };

  const handleVehicleSelect = (vehicleId) => {
    setSelectedVehicleForForm(vehicleId);
    const selectedVehicle = vehicles.find(v => v._id === vehicleId);

    if (selectedVehicle) {
      // Auto-populate all form data when vehicle is selected
      const newFormData = {
        vehicle: {
          year: selectedVehicle.year,
          make: selectedVehicle.make,
          model: selectedVehicle.model,
          vin: selectedVehicle.vin,
          price: selectedVehicle.price,
          mileage: selectedVehicle.mileage,
          color: selectedVehicle.color || '',
          bodyStyle: selectedVehicle.bodyType || selectedVehicle.bodyClass || '',
          engine: selectedVehicle.engine || '',
          transmission: selectedVehicle.transmission || '',
          fuelType: selectedVehicle.fuelType || '',
          doors: selectedVehicle.doors || '',
          drivetrain: selectedVehicle.drivetrain || ''
        },
        // Auto-populate dealer info
        dealer: {
          name: 'FazeNAuto',
          address: '1120 Meighen Way',
          phone: '************',
          email: '<EMAIL>',
          licenseNumber: 'OMVIC-PENDING' // Update when license is obtained
        },
        // Set default purchase amount to vehicle price
        purchaseAmount: selectedVehicle.price || 0,
        // Add default buyer information for testing
        buyer: {
          name: '',
          address: '',
          phone: '',
          email: '',
          driversLicense: ''
        },
        // Set default certification as "as-is"
        certification: 'as-is',
        // Add disclaimer text
        disclaimer: 'This vehicle is sold "as-is" without warranty. Buyer acknowledges that they have inspected the vehicle and accept its current condition. No liens or encumbrances exist on this vehicle unless otherwise disclosed.',
        // Initialize buyer/seller signature fields
        buyerSignature: {
          signed: false,
          signedAt: null,
          name: '',
          date: ''
        },
        sellerSignature: {
          signed: true, // Dealer signature
          signedAt: new Date().toISOString(),
          name: 'FazeNAuto',
          date: new Date().toLocaleDateString()
        }
      };

      setFormData(newFormData);
    }
  };

  const handleRinSelect = (rinId) => {
    setSelectedRinForForm(rinId);

    if (rinId === 'manual') {
      // Clear dealer information for manual entry
      setFormData(prev => ({
        ...prev,
        dealer: {
          name: '',
          address: '',
          phone: '',
          email: '',
          rin: '',
          omvicNumber: '',
          licenseNumber: '',
          contactPerson: {}
        }
      }));
    } else if (rinId) {
      const selectedRin = rins.find(r => r._id === rinId);
      if (selectedRin) {
        // Auto-populate dealer information from selected RIN
        setFormData(prev => ({
          ...prev,
          dealer: {
            name: selectedRin.businessName,
            address: selectedRin.address,
            phone: selectedRin.phone,
            email: selectedRin.email,
            rin: selectedRin.rin,
            omvicNumber: selectedRin.omvicNumber || '',
            licenseNumber: selectedRin.licenseNumber || '',
            contactPerson: selectedRin.contactPerson || {}
          }
        }));
      }
    } else {
      // Clear dealer information if no selection
      setFormData(prev => ({
        ...prev,
        dealer: {
          name: '',
          address: '',
          phone: '',
          email: '',
          rin: '',
          omvicNumber: '',
          licenseNumber: '',
          contactPerson: {}
        }
      }));
    }
  };

  const formTypes = [
    {
      id: 'bill-of-sale',
      name: 'Bill of Sale',
      icon: '📄',
      description: 'Official vehicle sale document with all required information',
      fields: ['vehicle', 'dealer', 'buyer', 'purchaseAmount', 'certification', 'disclaimer', 'buyerSignature', 'sellerSignature']
    },
    {
      id: 'omvic-disclosure',
      name: 'OMVIC Disclosure Form',
      icon: '⚖️',
      description: 'Mandatory disclosure form for Ontario dealers with vehicle history',
      fields: ['vehicle', 'dealer', 'buyer', 'disclosures', 'accidentHistory', 'brandingStatus', 'formerUse', 'mechanicalIssues', 'lienStatus', 'photos', 'buyerSignature', 'dealerSignature', 'date']
    },
    {
      id: 'hst-reminder',
      name: 'HST Return Reminder Form',
      icon: '💰',
      description: 'Quarterly HST return reminder with sales summary and filing status',
      fields: ['quarter', 'dateRange', 'totalTaxableSales', 'hstCollected', 'numberOfInvoices', 'markAsFiled']
    },
    {
      id: 'consignment-agreement',
      name: 'Consignment Agreement',
      icon: '🤝',
      description: 'Consignment sales agreement between consignor and FazeNAuto Inc.',
      fields: ['vehicle', 'consignor', 'disclosures', 'pricingTerms', 'agreementDuration', 'consignorSignature', 'consigneeSignature']
    }
  ];

  const handleFormSelect = (formId) => {
    setSelectedForm(formId);
    setFormData({});
    setResults(null);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCertificationChange = (value) => {
    let defaultDisclaimer = '';

    switch (value) {
      case 'certified':
        defaultDisclaimer = 'This vehicle has been inspected and meets the standards for certification in Ontario. It is being sold as a certified vehicle and is roadworthy at the time of sale. No liens or encumbrances exist unless otherwise disclosed.';
        break;
      case 'warranty':
        defaultDisclaimer = 'This vehicle is sold with a dealer-provided warranty. Warranty terms and coverage details have been reviewed and acknowledged by the buyer. No liens or encumbrances exist unless otherwise disclosed.';
        break;
      default: // 'as-is'
        defaultDisclaimer = 'This vehicle is sold "as-is" without warranty. Buyer acknowledges that they have inspected the vehicle and accept its current condition. No liens or encumbrances exist on this vehicle unless otherwise disclosed.';
    }

    setFormData(prev => ({
      ...prev,
      certification: value,
      disclaimer: defaultDisclaimer
    }));
  };

  const validateFormData = (formType, data) => {
    const errors = [];

    // Common required fields for all forms
    if (!data.vehicle?.make) errors.push('- Vehicle Make');
    if (!data.vehicle?.model) errors.push('- Vehicle Model');
    if (!data.vehicle?.year) errors.push('- Vehicle Year');
    if (!data.vehicle?.vin) errors.push('- Vehicle VIN');

    // Form-specific validations
    if (formType === 'bill-of-sale') {
      // Dealer Information - RIN is required
      if (!data.dealer?.rin) errors.push('- Dealer RIN');

      // Buyer Information
      if (!data.buyer?.name) errors.push('- Buyer Name');
      if (!data.buyer?.address) errors.push('- Buyer Address');
      if (!data.buyer?.driversLicense) errors.push('- Buyer Driver License');

      // Sale Details
      if (!data.purchaseAmount && data.purchaseAmount !== 0) errors.push('- Purchase Amount');

      // Certification is required
      if (!data.certification) errors.push('- Certification');

      // Invoice Mode is optional - defaults to 'test' if not provided

      // Note: Signatures are optional - can be signed after printing
    }

    if (formType === 'uvip-request') {
      // Only vehicle information is required for UVIP requests
    }

    if (formType === 'transfer-application') {
      if (!data.buyer?.name) errors.push('- Buyer Name');
      if (!data.buyer?.address) errors.push('- Buyer Address');
      if (!data.purchaseAmount && data.purchaseAmount !== 0) errors.push('- Purchase Amount');
      // Note: Signatures are optional - can be signed after printing
    }

    if (formType === 'consignment-agreement') {
      // Consignor Information
      if (!data.consignor?.name) errors.push('- Consignor Name');

      // Agreement Duration
      if (!data.agreementDuration?.from) errors.push('- Agreement Start Date');
      if (!data.agreementDuration?.to) errors.push('- Agreement End Date');

      // Note: Signatures, disclosures, and pricing terms are optional - can be customized
    }

    return errors;
  };

  const handleNestedInputChange = (parent, field, value) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent],
        [field]: value
      }
    }));
  };

  const handleValidationModalClose = () => {
    setValidationModal({ isOpen: false, errors: [] });
  };

  const handleValidationModalContinue = () => {
    const missingFields = validationModal.errors;

    if (missingFields.length > 0) {
      const confirmMessage = `Are you sure you want to continue?\n\nThe following required fields are missing:\n${missingFields.join('\n')}\n\nSome information may need to be filled manually after printing.`;

      if (window.confirm(confirmMessage)) {
        setValidationModal({ isOpen: false, errors: [] });
        // Continue with form generation despite validation errors
        proceedWithFormGeneration();
      }
    } else {
      setValidationModal({ isOpen: false, errors: [] });
      proceedWithFormGeneration();
    }
  };

  const proceedWithFormGeneration = async () => {
    setGenerating(true);
    setResults(null);

    try {
      const response = await fetch('/api/compliance-forms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          formType: selectedForm,
          data: formData,
          saveToFile: false
        })
      });

      if (response.ok) {
        // Handle PDF download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${selectedForm}-${Date.now()}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        setResults({
          success: true,
          message: `${selectedForm} generated successfully!`,
          downloadUrl: url
        });

        // Call onFormGenerated callback if provided
        if (onFormGenerated) {
          onFormGenerated(selectedForm, formData);
        }
      } else {
        const errorData = await response.json();
        setResults({
          success: false,
          message: errorData.error || 'Failed to generate form'
        });
      }
    } catch (error) {
      console.error('Form generation error:', error);
      setResults({
        success: false,
        message: 'Network error occurred while generating form'
      });
    } finally {
      setGenerating(false);
    }
  };

  const generateForm = async () => {
    if (!selectedForm) {
      alert('Please select a form type');
      return;
    }

    // Validate required fields based on form type
    const validationErrors = validateFormData(selectedForm, formData);
    if (validationErrors.length > 0) {
      // Show validation modal instead of alert
      setValidationModal({
        isOpen: true,
        errors: validationErrors
      });
      return;
    }

    // If validation passes, proceed with form generation
    await proceedWithFormGeneration();
  };

  const renderFormFields = () => {
    const selectedFormType = formTypes.find(f => f.id === selectedForm);
    if (!selectedFormType) return null;

    switch (selectedForm) {
      case 'bill-of-sale':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Vehicle Information</h5>
              <div className={styles.vehicleEntryToggle}>
                <label>
                  <input
                    type="checkbox"
                    checked={manualVehicleEntry}
                    onChange={(e) => {
                      setManualVehicleEntry(e.target.checked);
                      if (e.target.checked) {
                        setSelectedVehicleForForm('');
                        // Clear vehicle data when switching to manual entry
                        setFormData(prev => ({ ...prev, vehicle: {} }));
                      }
                    }}
                  />
                  Enter vehicle information manually
                </label>
              </div>
              {!manualVehicleEntry && (
                <div className={styles.vehicleSelector}>
                  <select
                    onChange={(e) => handleVehicleSelect(e.target.value)}
                    className="formInput"
                    disabled={loadingVehicles}
                    value={selectedVehicleForForm}
                  >
                    <option value="">Select a vehicle from inventory</option>
                    {vehicles.map(vehicle => (
                      <option key={vehicle._id} value={vehicle._id}>
                        {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.vin}
                      </option>
                    ))}
                  </select>
                  {loadingVehicles && <span>Loading vehicles...</span>}
                </div>
              )}
              <div className={styles.fieldsGrid}>
                <input
                  type="number"
                  placeholder="Year"
                  value={formData.vehicle?.year || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'year', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Make"
                  value={formData.vehicle?.make || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'make', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Model"
                  value={formData.vehicle?.model || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'model', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="VIN"
                  value={formData.vehicle?.vin || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'vin', e.target.value.toUpperCase())}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Mileage (km)"
                  value={formData.vehicle?.mileage || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'mileage', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Color"
                  value={formData.vehicle?.color || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'color', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Dealer Information</h5>
              <div className={styles.rinSelector}>
                <select
                  onChange={(e) => handleRinSelect(e.target.value)}
                  className="formInput"
                  disabled={loadingRins}
                  value={selectedRinForForm}
                >
                  <option value="">Select Registered Dealer / RIN</option>
                  {rins.map(rin => (
                    <option key={rin._id} value={rin._id}>
                      {rin.displayName || `${rin.businessName} (RIN: ${rin.rin})`}
                    </option>
                  ))}
                  <option value="manual">Manual Entry (Custom Dealer)</option>
                </select>
                {loadingRins && <span>Loading RINs...</span>}
              </div>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="Dealer Name"
                  value={formData.dealer?.name || ''}
                  onChange={(e) => handleNestedInputChange('dealer', 'name', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Address"
                  value={formData.dealer?.address || ''}
                  onChange={(e) => handleNestedInputChange('dealer', 'address', e.target.value)}
                  className="formInput"
                />
                <input
                  type="tel"
                  placeholder="Phone"
                  value={formData.dealer?.phone || ''}
                  onChange={(e) => handleNestedInputChange('dealer', 'phone', e.target.value)}
                  className="formInput"
                />
                <input
                  type="email"
                  placeholder="Email"
                  value={formData.dealer?.email || ''}
                  onChange={(e) => handleNestedInputChange('dealer', 'email', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Registration Identification Number (RIN)"
                  value={formData.dealer?.rin || ''}
                  onChange={(e) => handleNestedInputChange('dealer', 'rin', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="OMVIC Registration Number (Optional)"
                  value={formData.dealer?.omvicNumber || ''}
                  onChange={(e) => handleNestedInputChange('dealer', 'omvicNumber', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Buyer Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="Full Name"
                  value={formData.buyer?.name || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'name', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Address"
                  value={formData.buyer?.address || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'address', e.target.value)}
                  className="formInput"
                />
                <input
                  type="tel"
                  placeholder="Phone"
                  value={formData.buyer?.phone || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'phone', e.target.value)}
                  className="formInput"
                />
                <input
                  type="email"
                  placeholder="Email"
                  value={formData.buyer?.email || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'email', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Driver's License"
                  value={formData.buyer?.driversLicense || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'driversLicense', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Sale Details</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="number"
                  placeholder="Purchase Amount ($)"
                  value={formData.purchaseAmount || ''}
                  onChange={(e) => handleInputChange('purchaseAmount', parseFloat(e.target.value) || 0)}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Trade-in Value ($)"
                  value={formData.tradeInValue || ''}
                  onChange={(e) => handleInputChange('tradeInValue', parseFloat(e.target.value) || 0)}
                  className="formInput"
                />
                <select
                  value={formData.paymentMethod || 'Cash'}
                  onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                  className="formInput"
                >
                  <option value="Cash">Cash</option>
                  <option value="Certified Cheque">Certified Cheque</option>
                  <option value="Bank Draft">Bank Draft</option>
                  <option value="Financing">Financing</option>
                </select>
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Certification</h5>
              <div className={styles.fieldsGrid}>
                <select
                  value={formData.certification || 'as-is'}
                  onChange={(e) => handleCertificationChange(e.target.value)}
                  className="formInput"
                >
                  <option value="as-is">Sold As-Is</option>
                  <option value="certified">Certified Pre-Owned</option>
                  <option value="warranty">With Warranty</option>
                </select>
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Invoice Mode</h5>
              <div className={styles.fieldsGrid}>
                <select
                  value={formData.invoiceMode || 'test'}
                  onChange={(e) => handleInputChange('invoiceMode', e.target.value)}
                  className="formInput"
                >
                  <option value="test">Test Mode (Development/Internal)</option>
                  <option value="prod">Production Mode (Live Sales)</option>
                </select>
              </div>
              <p className={styles.invoiceModeHelp}>
                {formData.invoiceMode === 'prod'
                  ? '🔴 Production mode generates official invoice numbers for live sales'
                  : '🟡 Test mode generates test invoice numbers for development/training'
                }
              </p>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Disclaimer</h5>
              <textarea
                placeholder="Disclaimer text (liens/ownership)"
                value={formData.disclaimer || ''}
                onChange={(e) => handleInputChange('disclaimer', e.target.value)}
                rows={3}
                className={`formInput ${styles.textarea}`}
              />
            </div>

            <div className={styles.fieldGroup}>
              <h5>Signatures (Optional - can be signed after printing)</h5>
              <div className={styles.signatureSection}>
                <div className={styles.signatureBox}>
                  <h6>Buyer Signature</h6>
                  <input
                    type="text"
                    placeholder="Buyer Name (optional)"
                    value={formData.buyerSignature?.name || ''}
                    onChange={(e) => handleNestedInputChange('buyerSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.buyerSignature?.date || ''}
                    onChange={(e) => handleNestedInputChange('buyerSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
                <div className={styles.signatureBox}>
                  <h6>Seller Signature</h6>
                  <input
                    type="text"
                    placeholder="Seller Name (optional)"
                    value={formData.sellerSignature?.name || ''}
                    onChange={(e) => handleNestedInputChange('sellerSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.sellerSignature?.date || ''}
                    onChange={(e) => handleNestedInputChange('sellerSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'uvip-request':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Vehicle Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="VIN"
                  value={formData.vehicle?.vin || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'vin', e.target.value.toUpperCase())}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Year"
                  value={formData.vehicle?.year || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'year', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Make"
                  value={formData.vehicle?.make || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'make', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Model"
                  value={formData.vehicle?.model || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'model', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>
          </div>
        );

      case 'omvic-disclosure':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Vehicle Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="VIN"
                  value={formData.vehicle?.vin || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'vin', e.target.value.toUpperCase())}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Year"
                  value={formData.vehicle?.year || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'year', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Make"
                  value={formData.vehicle?.make || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'make', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Model"
                  value={formData.vehicle?.model || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'model', e.target.value)}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Mileage (km)"
                  value={formData.vehicle?.mileage || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'mileage', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Additional Disclosures</h5>
              <textarea
                placeholder="Enter any additional disclosures (optional)"
                value={formData.disclosures?.join('\n') || ''}
                onChange={(e) => handleInputChange('disclosures', e.target.value.split('\n').filter(Boolean))}
                rows={4}
                className={`formInput ${styles.textarea}`}
              />
            </div>
          </div>
        );

      case 'hst-reminder':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Tax Period Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="Period (e.g., Q1 2024)"
                  value={formData.period || ''}
                  onChange={(e) => handleInputChange('period', e.target.value)}
                  className="formInput"
                />
                <input
                  type="date"
                  placeholder="Due Date"
                  value={formData.dueDate || ''}
                  onChange={(e) => handleInputChange('dueDate', e.target.value)}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Total HST Collected ($)"
                  value={formData.totalHSTCollected || ''}
                  onChange={(e) => handleInputChange('totalHSTCollected', parseFloat(e.target.value) || 0)}
                  className="formInput"
                />
              </div>
            </div>
          </div>
        );

      case 'safety-standards-certificate':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Vehicle Information</h5>
              <div className={styles.vehicleSelector}>
                <select
                  onChange={(e) => handleVehicleSelect(e.target.value)}
                  className="formInput"
                  disabled={loadingVehicles}
                  value={selectedVehicleForForm}
                >
                  <option value="">Select a vehicle from inventory</option>
                  {vehicles.map(vehicle => (
                    <option key={vehicle._id} value={vehicle._id}>
                      {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.vin}
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="VIN"
                  value={formData.vehicle?.vin || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'vin', e.target.value.toUpperCase())}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Year"
                  value={formData.vehicle?.year || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'year', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Make"
                  value={formData.vehicle?.make || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'make', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Model"
                  value={formData.vehicle?.model || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'model', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Certification Status</h5>
              <div className={styles.fieldsGrid}>
                <select
                  value={formData.certification || 'as-is'}
                  onChange={(e) => handleInputChange('certification', e.target.value)}
                >
                  <option value="as-is">Sold As-Is (No Safety Required)</option>
                  <option value="safety-required">Safety Certificate Required</option>
                  <option value="safety-completed">Safety Certificate Completed</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 'transfer-application':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Vehicle Information</h5>
              <div className={styles.vehicleSelector}>
                <select
                  onChange={(e) => handleVehicleSelect(e.target.value)}
                  className="formInput"
                  disabled={loadingVehicles}
                  value={selectedVehicleForForm}
                >
                  <option value="">Select a vehicle from inventory</option>
                  {vehicles.map(vehicle => (
                    <option key={vehicle._id} value={vehicle._id}>
                      {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.vin}
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="VIN"
                  value={formData.vehicle?.vin || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'vin', e.target.value.toUpperCase())}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Year"
                  value={formData.vehicle?.year || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'year', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Make"
                  value={formData.vehicle?.make || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'make', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Model"
                  value={formData.vehicle?.model || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'model', e.target.value)}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Purchase Amount ($)"
                  value={formData.purchaseAmount || ''}
                  onChange={(e) => handleInputChange('purchaseAmount', parseFloat(e.target.value) || 0)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Buyer Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="Full Name"
                  value={formData.buyer?.name || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'name', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Address"
                  value={formData.buyer?.address || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'address', e.target.value)}
                  className="formInput"
                />
                <input
                  type="tel"
                  placeholder="Phone"
                  value={formData.buyer?.phone || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'phone', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Driver's License"
                  value={formData.buyer?.driversLicense || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'driversLicense', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Signatures (Optional - can be signed after printing)</h5>
              <div className={styles.signatureSection}>
                <div className={styles.signatureBox}>
                  <h6>Buyer Signature</h6>
                  <input
                    type="text"
                    placeholder="Buyer Name"
                    value={formData.buyerSignature?.name || ''}
                    onChange={(e) => handleNestedInputChange('buyerSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.buyerSignature?.date || ''}
                    onChange={(e) => handleNestedInputChange('buyerSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
                <div className={styles.signatureBox}>
                  <h6>Seller Signature</h6>
                  <input
                    type="text"
                    placeholder="Seller Name"
                    value={formData.sellerSignature?.name || ''}
                    onChange={(e) => handleNestedInputChange('sellerSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.sellerSignature?.date || ''}
                    onChange={(e) => handleNestedInputChange('sellerSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'omvic-disclosure':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Vehicle Information</h5>
              <div className={styles.vehicleEntryToggle}>
                <label>
                  <input
                    type="checkbox"
                    checked={manualVehicleEntry}
                    onChange={(e) => {
                      setManualVehicleEntry(e.target.checked);
                      if (e.target.checked) {
                        setSelectedVehicleForForm('');
                        // Clear vehicle data when switching to manual entry
                        setFormData(prev => ({ ...prev, vehicle: {} }));
                      }
                    }}
                  />
                  Enter vehicle information manually
                </label>
              </div>
              {!manualVehicleEntry && (
                <div className={styles.vehicleSelector}>
                  <select
                    onChange={(e) => handleVehicleSelect(e.target.value)}
                    className="formInput"
                    disabled={loadingVehicles}
                    value={selectedVehicleForForm}
                  >
                    <option value="">Select a vehicle from inventory</option>
                    {vehicles.map(vehicle => (
                      <option key={vehicle._id} value={vehicle._id}>
                        {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.vin}
                      </option>
                    ))}
                  </select>
                  {loadingVehicles && <div className={styles.loading}>Loading vehicles...</div>}
                </div>
              )}
              {(manualVehicleEntry || selectedVehicleForForm) && (
                <div className={styles.fieldsGrid}>
                  <input
                    type="number"
                    placeholder="Year"
                    value={formData.vehicle?.year || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'year', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="Make"
                    value={formData.vehicle?.make || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'make', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="Model"
                    value={formData.vehicle?.model || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'model', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="VIN"
                    value={formData.vehicle?.vin || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'vin', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="KM/Mileage"
                    value={formData.vehicle?.mileage || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'mileage', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="Engine"
                    value={formData.vehicle?.engine || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'engine', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="Transmission"
                    value={formData.vehicle?.transmission || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'transmission', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="Color"
                    value={formData.vehicle?.color || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'color', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="text"
                    placeholder="Body Style"
                    value={formData.vehicle?.bodyStyle || ''}
                    onChange={(e) => handleNestedInputChange('vehicle', 'bodyStyle', e.target.value)}
                    className="formInput"
                  />
                </div>
              )}
            </div>

            <div className={styles.fieldGroup}>
              <h5>Buyer Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="Full Name"
                  value={formData.buyer?.name || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'name', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Address"
                  value={formData.buyer?.address || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'address', e.target.value)}
                  className="formInput"
                />
                <input
                  type="tel"
                  placeholder="Phone Number"
                  value={formData.buyer?.phone || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'phone', e.target.value)}
                  className="formInput"
                />
                <input
                  type="email"
                  placeholder="Email Address"
                  value={formData.buyer?.email || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'email', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Driver's License Number"
                  value={formData.buyer?.licenseNumber || ''}
                  onChange={(e) => handleNestedInputChange('buyer', 'licenseNumber', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Vehicle History Disclosures</h5>
              <div className={styles.checkboxGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.accidentHistory || false}
                    onChange={(e) => setFormData(prev => ({ ...prev, accidentHistory: e.target.checked }))}
                  />
                  <span>Vehicle has been in an accident</span>
                </label>
              </div>

              <div className={styles.fieldGroup}>
                <label>Branding Status:</label>
                <div className={styles.radioGroup}>
                  {['Clean', 'Rebuilt', 'Salvage'].map(status => (
                    <label key={status} className={styles.radioLabel}>
                      <input
                        type="radio"
                        name="brandingStatus"
                        value={status}
                        checked={formData.brandingStatus === status}
                        onChange={(e) => setFormData(prev => ({ ...prev, brandingStatus: e.target.value }))}
                      />
                      <span>{status}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className={styles.fieldGroup}>
                <label>Former Use:</label>
                <div className={styles.radioGroup}>
                  {['Personal', 'Rental', 'Taxi', 'Commercial'].map(use => (
                    <label key={use} className={styles.radioLabel}>
                      <input
                        type="radio"
                        name="formerUse"
                        value={use}
                        checked={formData.formerUse === use}
                        onChange={(e) => setFormData(prev => ({ ...prev, formerUse: e.target.value }))}
                      />
                      <span>{use}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className={styles.checkboxGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.mechanicalIssues || false}
                    onChange={(e) => setFormData(prev => ({ ...prev, mechanicalIssues: e.target.checked }))}
                  />
                  <span>Vehicle has major mechanical issues</span>
                </label>
              </div>

              <div className={styles.checkboxGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.lienStatus || false}
                    onChange={(e) => setFormData(prev => ({ ...prev, lienStatus: e.target.checked }))}
                  />
                  <span>Vehicle has liens or encumbrances</span>
                </label>
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Photo Upload (Optional)</h5>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => setFormData(prev => ({ ...prev, photos: Array.from(e.target.files) }))}
                className={styles.fileInput}
              />
              <p className={styles.fileNote}>Upload photos of vehicle condition, damage, or relevant documentation</p>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Signatures</h5>
              <div className={styles.signatureSection}>
                <div className={styles.signatureGroup}>
                  <label>Buyer Signature</label>
                  <input
                    type="text"
                    placeholder="Buyer Name"
                    value={formData.buyerSignature?.name || ''}
                    onChange={(e) => handleNestedInputChange('buyerSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.buyerSignature?.date || ''}
                    onChange={(e) => handleNestedInputChange('buyerSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
                <div className={styles.signatureGroup}>
                  <label>Dealer Signature</label>
                  <input
                    type="text"
                    placeholder="Dealer Representative Name"
                    value={formData.dealerSignature?.name || ''}
                    onChange={(e) => handleNestedInputChange('dealerSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.dealerSignature?.date || ''}
                    onChange={(e) => handleNestedInputChange('dealerSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'hst-reminder':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Tax Period Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="Quarter (e.g., Q2 2025)"
                  value={formData.quarter || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, quarter: e.target.value }))}
                  className="formInput"
                />
                <input
                  type="date"
                  placeholder="Period Start Date"
                  value={formData.dateRange?.start || ''}
                  onChange={(e) => handleNestedInputChange('dateRange', 'start', e.target.value)}
                  className="formInput"
                />
                <input
                  type="date"
                  placeholder="Period End Date"
                  value={formData.dateRange?.end || ''}
                  onChange={(e) => handleNestedInputChange('dateRange', 'end', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Sales Summary</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="number"
                  step="0.01"
                  placeholder="Total Taxable Sales ($)"
                  value={formData.totalTaxableSales || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, totalTaxableSales: parseFloat(e.target.value) || 0 }))}
                  className="formInput"
                />
                <input
                  type="number"
                  step="0.01"
                  placeholder="HST Collected ($)"
                  value={formData.hstCollected || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, hstCollected: parseFloat(e.target.value) || 0 }))}
                  className="formInput"
                />
                <input
                  type="number"
                  placeholder="Number of Invoices"
                  value={formData.numberOfInvoices || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, numberOfInvoices: parseInt(e.target.value) || 0 }))}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Filing Status</h5>
              <div className={styles.checkboxGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.markAsFiled || false}
                    onChange={(e) => setFormData(prev => ({ ...prev, markAsFiled: e.target.checked }))}
                  />
                  <span>Mark as Filed</span>
                </label>
              </div>
            </div>
          </div>
        );

      case 'consignment-agreement':
        return (
          <div className={styles.formFields}>
            <div className={styles.fieldGroup}>
              <h5>Vehicle Information</h5>
              <div className={styles.vehicleEntryToggle}>
                <label>
                  <input
                    type="checkbox"
                    checked={manualVehicleEntry}
                    onChange={(e) => {
                      setManualVehicleEntry(e.target.checked);
                      if (e.target.checked) {
                        setSelectedVehicleForForm('');
                        // Clear vehicle data when switching to manual entry
                        setFormData(prev => ({ ...prev, vehicle: {} }));
                      }
                    }}
                  />
                  Enter vehicle information manually
                </label>
              </div>
              {!manualVehicleEntry && (
                <div className={styles.vehicleSelector}>
                  <select
                    onChange={(e) => handleVehicleSelect(e.target.value)}
                    className="formInput"
                    disabled={loadingVehicles}
                    value={selectedVehicleForForm}
                  >
                    <option value="">Select a vehicle from inventory</option>
                    {vehicles.map(vehicle => (
                      <option key={vehicle._id} value={vehicle._id}>
                        {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.vin}
                      </option>
                    ))}
                  </select>
                  {loadingVehicles && <span>Loading vehicles...</span>}
                </div>
              )}
              <div className={styles.fieldsGrid}>
                <input
                  type="number"
                  placeholder="Year"
                  value={formData.vehicle?.year || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'year', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Make"
                  value={formData.vehicle?.make || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'make', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Model"
                  value={formData.vehicle?.model || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'model', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="VIN"
                  value={formData.vehicle?.vin || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'vin', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="KM/Mileage"
                  value={formData.vehicle?.mileage || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'mileage', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Engine"
                  value={formData.vehicle?.engine || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'engine', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Transmission"
                  value={formData.vehicle?.transmission || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'transmission', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Color"
                  value={formData.vehicle?.color || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'color', e.target.value)}
                  className="formInput"
                />
                <input
                  type="text"
                  placeholder="Body Style"
                  value={formData.vehicle?.bodyStyle || ''}
                  onChange={(e) => handleNestedInputChange('vehicle', 'bodyStyle', e.target.value)}
                  className="formInput"
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Consignor Information</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="text"
                  placeholder="Consignor Name *"
                  value={formData.consignor?.name || ''}
                  onChange={(e) => handleNestedInputChange('consignor', 'name', e.target.value)}
                  className="formInput"
                  required
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Disclosures & Declarations</h5>
              <div className={styles.dynamicFieldsContainer}>
                {(formData.disclosures || [
                  '(a) The Consignor is the true owner and has the right and title to sell the vehicle.',
                  '(b) Description of the vehicle is accurate, including the odometer reading.',
                  '(c) Disclosure of any prior use as taxi/police/rental and any liens or encumbrances.',
                  '(d) Details of repairs over $3,000 or any material deficiencies.',
                  '(e) Consignor certifies vehicle has not been written off or materially altered.',
                  '(f) The vehicle will not be used, operated, or taken off-site by the Consignee except for test drives authorized by the Consignor.'
                ]).map((disclosure, index) => (
                  <div key={index} className={styles.dynamicField}>
                    <textarea
                      value={disclosure}
                      onChange={(e) => {
                        const newDisclosures = [...(formData.disclosures || [
                          '(a) The Consignor is the true owner and has the right and title to sell the vehicle.',
                          '(b) Description of the vehicle is accurate, including the odometer reading.',
                          '(c) Disclosure of any prior use as taxi/police/rental and any liens or encumbrances.',
                          '(d) Details of repairs over $3,000 or any material deficiencies.',
                          '(e) Consignor certifies vehicle has not been written off or materially altered.',
                          '(f) The vehicle will not be used, operated, or taken off-site by the Consignee except for test drives authorized by the Consignor.'
                        ])];
                        newDisclosures[index] = e.target.value;
                        setFormData(prev => ({ ...prev, disclosures: newDisclosures }));
                      }}
                      className="formInput"
                      rows="2"
                      placeholder="Enter disclosure statement..."
                    />
                    {index > 5 && (
                      <button
                        type="button"
                        onClick={() => {
                          const newDisclosures = [...(formData.disclosures || [])];
                          newDisclosures.splice(index, 1);
                          setFormData(prev => ({ ...prev, disclosures: newDisclosures }));
                        }}
                        className={styles.removeButton}
                      >
                        ×
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => {
                    const newDisclosures = [...(formData.disclosures || [
                      '(a) The Consignor is the true owner and has the right and title to sell the vehicle.',
                      '(b) Description of the vehicle is accurate, including the odometer reading.',
                      '(c) Disclosure of any prior use as taxi/police/rental and any liens or encumbrances.',
                      '(d) Details of repairs over $3,000 or any material deficiencies.',
                      '(e) Consignor certifies vehicle has not been written off or materially altered.',
                      '(f) The vehicle will not be used, operated, or taken off-site by the Consignee except for test drives authorized by the Consignor.'
                    ]), ''];
                    setFormData(prev => ({ ...prev, disclosures: newDisclosures }));
                  }}
                  className={styles.addButton}
                >
                  + Add Disclosure
                </button>
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Pricing and Sales Terms</h5>
              <div className={styles.dynamicFieldsContainer}>
                {(formData.pricingTerms || [
                  `(a) Agreement duration: from ${formData.agreementDuration?.from || '_'.repeat(12)} to ${formData.agreementDuration?.to || '_'.repeat(12)}`,
                  '(c) All written offers to be presented to consignor unless otherwise agreed.',
                  '(d) All funds held in trust and payout due within 14 days of vehicle sale.'
                ]).map((term, index) => (
                  <div key={index} className={styles.dynamicField}>
                    <textarea
                      value={term}
                      onChange={(e) => {
                        const newTerms = [...(formData.pricingTerms || [
                          `(a) Agreement duration: from ${formData.agreementDuration?.from || '_'.repeat(12)} to ${formData.agreementDuration?.to || '_'.repeat(12)}`,
                          '(c) All written offers to be presented to consignor unless otherwise agreed.',
                          '(d) All funds held in trust and payout due within 14 days of vehicle sale.'
                        ])];
                        newTerms[index] = e.target.value;
                        setFormData(prev => ({ ...prev, pricingTerms: newTerms }));
                      }}
                      className="formInput"
                      rows="2"
                      placeholder="Enter pricing/sales term..."
                    />
                    {index > 2 && (
                      <button
                        type="button"
                        onClick={() => {
                          const newTerms = [...(formData.pricingTerms || [])];
                          newTerms.splice(index, 1);
                          setFormData(prev => ({ ...prev, pricingTerms: newTerms }));
                        }}
                        className={styles.removeButton}
                      >
                        ×
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={() => {
                    const newTerms = [...(formData.pricingTerms || [
                      `(a) Agreement duration: from ${formData.agreementDuration?.from || '_'.repeat(12)} to ${formData.agreementDuration?.to || '_'.repeat(12)}`,
                      '(c) All written offers to be presented to consignor unless otherwise agreed.',
                      'All funds held in trust and payout due within 14 days of vehicle sale.'
                    ]), ''];
                    setFormData(prev => ({ ...prev, pricingTerms: newTerms }));
                  }}
                  className={styles.addButton}
                >
                  + Add Term
                </button>
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Agreement Duration</h5>
              <div className={styles.fieldsGrid}>
                <input
                  type="date"
                  placeholder="From Date *"
                  value={formData.agreementDuration?.from || ''}
                  onChange={(e) => {
                    handleNestedInputChange('agreementDuration', 'from', e.target.value);
                    // Update the first pricing term with new dates
                    if (formData.pricingTerms && formData.pricingTerms[0]) {
                      const newTerms = [...formData.pricingTerms];
                      newTerms[0] = `(a) Agreement duration: from ${e.target.value || '_'.repeat(12)} to ${formData.agreementDuration?.to || '_'.repeat(12)}`;
                      setFormData(prev => ({ ...prev, pricingTerms: newTerms }));
                    }
                  }}
                  className="formInput"
                  required
                />
                <input
                  type="date"
                  placeholder="To Date *"
                  value={formData.agreementDuration?.to || ''}
                  onChange={(e) => {
                    handleNestedInputChange('agreementDuration', 'to', e.target.value);
                    // Update the first pricing term with new dates
                    if (formData.pricingTerms && formData.pricingTerms[0]) {
                      const newTerms = [...formData.pricingTerms];
                      newTerms[0] = `(a) Agreement duration: from ${formData.agreementDuration?.from || '_'.repeat(12)} to ${e.target.value || '_'.repeat(12)}`;
                      setFormData(prev => ({ ...prev, pricingTerms: newTerms }));
                    }
                  }}
                  className="formInput"
                  required
                />
              </div>
            </div>

            <div className={styles.fieldGroup}>
              <h5>Signature Information</h5>
              <div className={styles.signatureSection}>
                <div className={styles.signatureField}>
                  <label>Consignor Signature</label>
                  <input
                    type="text"
                    placeholder="Consignor signature (optional - can be signed after printing)"
                    value={formData.consignorSignature?.name || ''}
                    onChange={(e) => handleNestedInputChange('consignorSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.consignorSignature?.date || ''}
                    onChange={(e) => handleNestedInputChange('consignorSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
                <div className={styles.signatureField}>
                  <label>Consignee (FazeNAuto) Signature</label>
                  <input
                    type="text"
                    placeholder="FazeNAuto representative"
                    value={formData.consigneeSignature?.name || 'FazeNAuto Inc.'}
                    onChange={(e) => handleNestedInputChange('consigneeSignature', 'name', e.target.value)}
                    className="formInput"
                  />
                  <input
                    type="date"
                    placeholder="Date"
                    value={formData.consigneeSignature?.date || new Date().toISOString().split('T')[0]}
                    onChange={(e) => handleNestedInputChange('consigneeSignature', 'date', e.target.value)}
                    className="formInput"
                  />
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={styles.complianceFormsPanel}>
      <div className={styles.header}>
        <h3>📋 Compliance Forms Generator</h3>
        <p>Generate required dealership forms and documents</p>
        {selectedVehicles.length > 0 && (
          <span className={styles.selectedCount}>
            {selectedVehicles.length} vehicle(s) selected
          </span>
        )}
      </div>

      {/* Vehicle Selection for Form */}
      {selectedVehicles.length > 0 && (
        <div className={styles.section}>
          <h4>Select Vehicle for Form</h4>
          <select
            value={selectedVehicleForForm}
            onChange={(e) => {
              setSelectedVehicleForForm(e.target.value);
              handleVehicleSelect(e.target.value);
            }}
            className="formInput"
          >
            <option value="">Choose a vehicle...</option>
            {selectedVehicles.map(vehicleId => {
              const vehicle = vehicles.find(v => v._id === vehicleId);
              return vehicle ? (
                <option key={vehicleId} value={vehicleId}>
                  {vehicle.year} {vehicle.make} {vehicle.model} - {vehicle.vin?.slice(-6)}
                </option>
              ) : null;
            })}
          </select>
        </div>
      )}

      {/* Form Type Selection */}
      <div className={styles.section}>
        <h4>Select Form Type</h4>
        <div className={styles.formTypesGrid}>
          {formTypes.map(form => (
            <div
              key={form.id}
              className={`${styles.formTypeCard} ${selectedForm === form.id ? styles.selected : ''}`}
              onClick={() => handleFormSelect(form.id)}
            >
              <div className={styles.formIcon}>{form.icon}</div>
              <div className={styles.formInfo}>
                <h5>{form.name}</h5>
                <p>{form.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Form Fields */}
      {selectedForm && (
        <div className={styles.section}>
          <h4>Form Details</h4>
          {renderFormFields()}
        </div>
      )}

      {/* Generate Button */}
      {selectedForm && (
        <div className={styles.generateSection}>
          <button
            onClick={generateForm}
            disabled={generating}
            className={styles.generateButton}
          >
            {generating ? '🔄 Generating PDF...' : '📄 Generate PDF'}
          </button>
        </div>
      )}

      {/* Results */}
      {results && (
        <div className={styles.results}>
          {results.success ? (
            <div className={styles.successMessage}>
              ✅ {results.message}
            </div>
          ) : (
            <div className={styles.errorMessage}>
              ❌ {results.error}
            </div>
          )}
        </div>
      )}

      {/* Info Note */}
      <div className={styles.infoNote}>
        <h4>📝 Important Notes</h4>
        <ul>
          <li>All forms are pre-filled with FazeNAuto dealer information</li>
          <li>Forms comply with Ontario regulations and OMVIC requirements</li>
          <li>Generated PDFs can be printed or emailed to customers</li>
          <li>Keep copies of all completed forms for your records</li>
        </ul>
      </div>

      <ValidationModal
        isOpen={validationModal.isOpen}
        onClose={handleValidationModalClose}
        onContinue={handleValidationModalContinue}
        errors={validationModal.errors}
        title="Required Fields Missing"
      />
    </div>
  );
}
