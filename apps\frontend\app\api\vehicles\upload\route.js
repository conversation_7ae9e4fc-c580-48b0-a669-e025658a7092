import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import crypto from 'crypto';
import Vehicle from '../../../../lib/models/Vehicle';
import { connectToDatabase } from '../../../../lib/dbConnect';
import { NextResponse } from 'next/server';

const s3 = new S3Client({
  region: process.env.CUSTOM_AWS_REGION,
  credentials: {
    accessKeyId: process.env.CUSTOM_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.CUSTOM_AWS_SECRET_ACCESS_KEY,
  },
});

// Configure the API route for file uploads
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const maxDuration = 60; // 60 seconds timeout

export async function POST(request) {
  try {
    await connectToDatabase();

    // 🔒 AUTHENTICATION CHECK - CRITICAL SECURITY
    const authHeader = request.headers.get('authorization');
    const userAgent = request.headers.get('user-agent') || '';

    // Check if request is coming from our frontend (basic check)
    if (!userAgent.includes('Mozilla') && !authHeader) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized access. This endpoint requires authentication.'
      }, { status: 401 });
    }

    // Additional security: Check for user session data in form
    const form = await request.formData();
    const userEmail = form.get('userEmail');
    const userRole = form.get('userRole');

    if (!userEmail || !userRole) {
      return NextResponse.json({
        success: false,
        error: 'Authentication required. Please log in to upload vehicles.'
      }, { status: 401 });
    }

    // Validate user role
    const allowedRoles = ['admin', 'dealer', 'salesman'];
    if (!allowedRoles.includes(userRole)) {
      return NextResponse.json({
        success: false,
        error: 'Insufficient permissions. Only authorized dealers can upload vehicles.'
      }, { status: 403 });
    }

    console.log(`🔒 Authenticated upload request from: ${userEmail} (${userRole})`);

    // 🔍 DEBUG: Log all incoming form data keys and values
    for (let [key, value] of form.entries()) {
      console.log(`${key}: ${value}`);
    }

    const imageFiles = form.getAll('images');
    const videoFile = form.get('video');
    const vin = form.get('vin');
    const make = form.get('make');
    const model = form.get('model');
    const year = form.get('year');
    const color = form.get('color');
    const price = form.get('price');
    const mileage = form.get('mileage');
    const engine = form.get('engine');
    const drivetrain = form.get('drivetrain');
    const transmission = form.get('transmission');

    // Validate required fields
    const requiredFields = { vin, make, model, year, color, price, mileage, engine, drivetrain, transmission };
    const missingFields = Object.entries(requiredFields)
      .filter(([key, value]) => !value || value === '')
      .map(([key]) => key);

    // Debug: Log all field values
    console.log('🔍 Field validation:');
    Object.entries(requiredFields).forEach(([key, value]) => {
      console.log(`  ${key}: "${value}" (${typeof value})`);
    });

    if (missingFields.length > 0) {
      console.log('🔍 Missing fields:', missingFields);
      return NextResponse.json({
        success: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      }, { status: 400 });
    }

    // 🔁 1. Check for duplicate VIN
    const vinExists = await Vehicle.findOne({ vin });
    if (vinExists) {
      return NextResponse.json({ success: false, error: 'A vehicle with this VIN already exists.' }, { status: 409 });
    }

    // Validate image files
    if (!imageFiles || imageFiles.length === 0) {
      return NextResponse.json({ success: false, error: 'At least one image is required.' }, { status: 400 });
    }

    if (imageFiles.length > 50) {
      return NextResponse.json({ success: false, error: 'Maximum 50 images allowed.' }, { status: 400 });
    }

    // Process and upload all images
    const imageUrls = [];
    const imageHashes = [];

    for (let i = 0; i < imageFiles.length; i++) {
      const imageFile = imageFiles[i];
      const buffer = Buffer.from(await imageFile.arrayBuffer());

      // 🔁 2. Check for duplicate image using hash
      const hash = crypto.createHash('sha256').update(buffer).digest('hex');
      const hashExists = await Vehicle.findOne({ imageHash: hash });
      if (hashExists) {
        return NextResponse.json({ success: false, error: `Duplicate image detected: ${imageFile.name}` }, { status: 409 });
      }

      // 🆕 Structured file naming: vehicles/{make}/{model}/{year}/{uuid}.{ext}
      const uuid = uuidv4();
      const fileExtension = path.extname(imageFile.name);
      const safeMake = make.replace(/\s+/g, '-').toLowerCase();
      const safeModel = model.replace(/\s+/g, '-').toLowerCase();
      const fileKey = `vehicles/${safeMake}/${safeModel}/${year}/${uuid}${fileExtension}`;

      const uploadParams = {
        Bucket: process.env.CUSTOM_AWS_S3_BUCKET_NAME,
        Key: fileKey,
        Body: buffer,
        ContentType: imageFile.type,
        CacheControl: 'public, max-age=31536000',
      };

      await s3.send(new PutObjectCommand(uploadParams));

      const imageUrl = `https://${process.env.CUSTOM_AWS_S3_BUCKET_NAME}.s3.${process.env.CUSTOM_AWS_REGION}.amazonaws.com/${fileKey}`;
      imageUrls.push(imageUrl);
      imageHashes.push(hash);
    }

    // Use first image as primary image for backward compatibility
    const primaryImageUrl = imageUrls[0];
    const primaryImageHash = imageHashes[0];

    // Process video file if provided
    let videoUrl = null;
    if (videoFile && videoFile.size > 0) {
      // Validate video file type
      const allowedVideoTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'];
      if (!allowedVideoTypes.includes(videoFile.type)) {
        return NextResponse.json({ success: false, error: 'Invalid video format. Allowed formats: MP4, AVI, MOV, WMV, WebM' }, { status: 400 });
      }

      // Check video file size (limit to 100MB)
      const maxVideoSize = 100 * 1024 * 1024; // 100MB in bytes
      if (videoFile.size > maxVideoSize) {
        return NextResponse.json({ success: false, error: 'Video file too large. Maximum size is 100MB.' }, { status: 400 });
      }

      const videoBuffer = Buffer.from(await videoFile.arrayBuffer());
      const videoUuid = uuidv4();
      const videoExtension = path.extname(videoFile.name);
      const safeMake = make.replace(/\s+/g, '-').toLowerCase();
      const safeModel = model.replace(/\s+/g, '-').toLowerCase();
      const videoFileKey = `vehicles/${safeMake}/${safeModel}/${year}/videos/${videoUuid}${videoExtension}`;

      const videoUploadParams = {
        Bucket: process.env.CUSTOM_AWS_S3_BUCKET_NAME,
        Key: videoFileKey,
        Body: videoBuffer,
        ContentType: videoFile.type,
        CacheControl: 'public, max-age=31536000',
      };

      await s3.send(new PutObjectCommand(videoUploadParams));
      videoUrl = `https://${process.env.CUSTOM_AWS_S3_BUCKET_NAME}.s3.${process.env.CUSTOM_AWS_REGION}.amazonaws.com/${videoFileKey}`;
    }

    // Parse features data
    let features = {
      exterior: [],
      interior: [],
      mechanical: [],
      safety: [],
      entertainment: []
    };

    const featuresData = form.get('features');
    if (featuresData) {
      try {
        features = JSON.parse(featuresData);
      } catch (error) {
        console.error('Error parsing features data:', error);
      }
    }

    // Helper function to extract number from string like "4 Doors" -> 4
    const extractNumber = (str) => {
      if (!str) return null;
      const match = str.match(/(\d+)/);
      return match ? parseInt(match[1]) : null;
    };

    const vehicleData = {
      make,
      model,
      year: parseInt(year),
      vin,
      mileage: parseInt(mileage),
      color,
      price: parseFloat(price),
      engine,
      drivetrain,
      transmission,
      status: 'active', // Set vehicles to active by default so they appear on customer pages
      features, // Add features to vehicle data
      // Additional fields from admin upload form
      fuelType: form.get('fuelType'),
      bodyType: form.get('bodyClass'), // Map bodyClass to bodyType for consistency
      doors: extractNumber(form.get('doors')), // Extract number from "4 Doors" -> 4
      cylinders: extractNumber(form.get('cylinders')), // Extract number from "4 Cylinders" -> 4
      imageUrl: primaryImageUrl, // Primary image for backward compatibility
      images: imageUrls, // All images array
      videoUrl: videoUrl, // Video URL if provided
      imageHash: primaryImageHash, // Primary image hash for duplicate detection
      description: form.get('description'), // Add description field
    };

    const vehicle = await Vehicle.create(vehicleData);

    

    return NextResponse.json({
      success: true,
      message: 'Vehicle uploaded successfully!',
      data: vehicle
    });
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}
