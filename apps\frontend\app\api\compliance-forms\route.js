import { NextResponse } from 'next/server';
import PDFGenerator from '../../../lib/pdfGenerator.js';
import { generateInvoiceNumber } from '../../../lib/utils/invoiceGenerator.js';

/**
 * POST /api/compliance-forms - Generate compliance forms
 */
export async function POST(request) {
  try {
    const body = await request.json();
    const { 
      formType, 
      data,
      saveToFile = false 
    } = body;

    if (!formType) {
      return NextResponse.json({
        success: false,
        error: 'Form type is required'
      }, { status: 400 });
    }

    const validFormTypes = ['bill-of-sale', 'omvic-disclosure', 'hst-reminder', 'consignment-agreement'];
    if (!validFormTypes.includes(formType)) {
      return NextResponse.json({
        success: false,
        error: `Invalid form type. Valid types: ${validFormTypes.join(', ')}`
      }, { status: 400 });
    }

    const generator = new PDFGenerator();
    let pdfBuffer;
    let filename;

    switch (formType) {
      case 'bill-of-sale':
        if (!data.vehicle || !data.buyer || (!data.purchaseAmount && data.purchaseAmount !== 0)) {
          return NextResponse.json({
            success: false,
            error: 'Vehicle, buyer, and purchase amount information required for Bill of Sale'
          }, { status: 400 });
        }

        // Generate invoice number based on mode
        const invoiceMode = data.invoiceMode || 'test';
        console.log(`🔢 Generating invoice for mode: ${invoiceMode}`);

        const invoiceNumber = await generateInvoiceNumber(invoiceMode);
        console.log(`📄 Generated invoice number: ${invoiceNumber}`);

        // Add invoice number to data
        const billOfSaleData = {
          ...data,
          invoiceNumber
        };

        pdfBuffer = await generator.generateBillOfSale(billOfSaleData);
        filename = `bill-of-sale-${invoiceNumber.replace(/[^a-zA-Z0-9]/g, '-')}.pdf`;
        break;

      case 'omvic-disclosure':
        if (!data.vehicle || !data.buyer) {
          return NextResponse.json({
            success: false,
            error: 'Vehicle and buyer information required for OMVIC disclosure'
          }, { status: 400 });
        }
        pdfBuffer = await generator.generateOMVICDisclosure(data);
        filename = `omvic-disclosure-${data.vehicle.vin || Date.now()}.pdf`;
        break;

      case 'hst-reminder':
        if (!data.quarter || !data.dateRange) {
          return NextResponse.json({
            success: false,
            error: 'Quarter and date range required for HST reminder'
          }, { status: 400 });
        }
        pdfBuffer = await generator.generateHSTReminder(data);
        filename = `hst-reminder-${data.quarter.replace(/\s+/g, '-')}.pdf`;
        break;

      case 'consignment-agreement':
        if (!data.vehicle || !data.consignor) {
          return NextResponse.json({
            success: false,
            error: 'Vehicle and consignor information required for Consignment Agreement'
          }, { status: 400 });
        }
        pdfBuffer = await generator.generateConsignmentAgreement(data);
        filename = `consignment-agreement-${data.vehicle.vin || Date.now()}.pdf`;
        break;

      default:
        throw new Error('Unsupported form type');
    }

    // Save to file if requested
    let filePath = null;
    if (saveToFile) {
      filePath = await generator.savePDF(pdfBuffer, filename);
    }

    // Return PDF as blob for download
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('❌ Compliance form generation error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

/**
 * GET /api/compliance-forms - Get available form types and templates
 */
export async function GET(request) {
  try {
    const formTypes = [
      {
        id: 'bill-of-sale',
        name: 'Bill of Sale',
        description: 'Official vehicle sale document with all required information',
        requiredFields: ['vehicle', 'buyer', 'salePrice'],
        optionalFields: ['tradeInValue', 'warranties', 'paymentMethod']
      },
      {
        id: 'omvic-disclosure',
        name: 'OMVIC Disclosure Form',
        description: 'Mandatory disclosure form for Ontario dealers with vehicle history',
        requiredFields: ['vehicle', 'buyer', 'brandingStatus', 'formerUse'],
        optionalFields: ['accidentHistory', 'mechanicalIssues', 'lienStatus', 'photos']
      },
      {
        id: 'hst-reminder',
        name: 'HST Return Reminder Form',
        description: 'Quarterly HST return reminder with sales summary and filing status',
        requiredFields: ['quarter', 'dateRange', 'totalTaxableSales', 'hstCollected'],
        optionalFields: ['numberOfInvoices', 'markAsFiled']
      },
      {
        id: 'consignment-agreement',
        name: 'Consignment Agreement',
        description: 'Consignment sales agreement between consignor and FazeNAuto Inc.',
        requiredFields: ['vehicle', 'consignor', 'agreementDuration'],
        optionalFields: ['consignorSignature', 'consigneeSignature']
      }
    ];

    const sampleData = {
      vehicle: {
        year: 2020,
        make: 'Honda',
        model: 'Civic',
        vin: '1HGBH41JXMN109186',
        mileage: 50000,
        color: 'Silver',
        bodyStyle: 'Sedan',
        engine: '2.0L 4-Cylinder',
        transmission: 'Automatic',
        fuelType: 'Gasoline',
        doors: 4
      },
      dealer: {
        name: 'FazeNAuto',
        address: '1120 Meighen Way',
        phone: '************',
        email: '<EMAIL>',
        licenseNumber: 'OMVIC-PENDING'
      },
      buyer: {
        name: 'John Smith',
        address: '123 Main St, Toronto, ON M1A 1A1',
        phone: '************',
        email: '<EMAIL>',
        driversLicense: 'S1234-56789-01234'
      },
      purchaseAmount: 18500,
      tradeInValue: 2000,
      paymentMethod: 'Certified Cheque',
      certification: 'as-is',
      disclaimer: 'This vehicle is sold "as-is" without warranty. Buyer acknowledges that they have inspected the vehicle and accept its current condition. No liens or encumbrances exist on this vehicle unless otherwise disclosed.',
      buyerSignature: {
        signed: false,
        signedAt: null,
        name: '',
        date: ''
      },
      sellerSignature: {
        signed: true,
        signedAt: new Date().toISOString(),
        name: 'FazeNAuto',
        date: new Date().toLocaleDateString()
      }
    };

    return NextResponse.json({
      success: true,
      formTypes,
      sampleData,
      dealerInfo: {
        name: 'FazeNAuto',
        address: '1120 Meighen Way',
        phone: '************',
        email: '<EMAIL>',
        licenseNumber: 'OMVIC-PENDING'
      }
    });

  } catch (error) {
    console.error('❌ Compliance forms info error:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
