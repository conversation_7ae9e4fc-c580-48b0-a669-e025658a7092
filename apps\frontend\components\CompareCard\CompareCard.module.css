/* Compare Card Component */

.compareCard {
  background: var(--bg-primary, white);
  border: 1px solid var(--border-primary, #e2e8f0);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--card-shadow);
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Dark mode styling */
[data-theme="dark"] .compareCard {
  background: var(--bg-secondary);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Remove hover effects for comparison page */

/* Remove Button */
.removeBtn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid white;
  background: rgba(239, 68, 68, 0.9);
  color: white;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
  line-height: 1;
}

.removeBtn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* Vehicle Image */
.imageWrapper {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.vehicleImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Vehicle Info */
.vehicleInfo {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.vehicleTitle {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  line-height: 1.3;
}

/* Price Section */
.priceSection {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-primary, #e2e8f0);
}

.price {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-primary, #2d3748);
  display: block;
  margin-bottom: 0.25rem;
}

.taxText {
  font-size: 0.9rem;
  color: var(--text-tertiary, #94a3b8);
}

/* Vehicle Overview Section */
.overviewSection {
  margin-bottom: 1.5rem;
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--accent-primary, #4299e1);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.overviewGrid {
  display: grid;
  gap: 0.75rem;
}

.overviewItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.overviewItem:last-child {
  border-bottom: none;
}

.overviewLabel {
  font-weight: 600;
  color: var(--text-secondary, #718096);
  font-size: 0.85rem;
}

.overviewValue {
  font-weight: 500;
  color: var(--text-primary, #2d3748);
  font-size: 0.85rem;
  text-align: right;
}

/* Features Section */
.featuresSection {
  margin-bottom: 1.5rem;
}

[data-theme="dark"] .featuresSection {
  background: rgba(255, 255, 255, 0.05);
}

.featuresList {
  display: grid;
  gap: 0.5rem;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.featureIcon {
  color: var(--accent-secondary, #10b981);
  font-weight: bold;
  font-size: 0.8rem;
}

.featureText {
  color: var(--text-secondary, #718096);
  font-weight: 500;
}

/* Specifications Section */
.specificationsSection {
  margin-bottom: 1.5rem;
}

.specificationsList {
  display: grid;
  gap: 0.5rem;
}

.specificationItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.specificationItem:last-child {
  border-bottom: none;
}

.specificationLabel {
  font-weight: 500;
  color: var(--text-secondary, #718096);
  font-size: 0.85rem;
}

.specificationValue {
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  font-size: 0.85rem;
  text-align: right;
}

/* Action Buttons */
.actionButtons {
  display: flex;
  gap: 0.75rem;
  margin-top: auto;
}

.viewDetailsBtn,
.contactBtn {
  flex: 1;
  padding: 0.875rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  text-align: center;
}

.viewDetailsBtn {
  background: var(--accent-primary, #4299e1);
  color: white;
}

.viewDetailsBtn:hover {
  background: var(--accent-hover, #3182ce);
}

.contactBtn {
  background: var(--accent-secondary, #10b981);
  color: white;
}

.contactBtn:hover {
  background: #0d9488;
}

/* Responsive Design */
@media (max-width: 768px) {
  .vehicleInfo {
    padding: 1rem;
  }

  .vehicleTitle {
    font-size: 1.2rem;
  }

  .price {
    font-size: 1.5rem;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .featuresSection {
    padding: 0.75rem;
  }

  .imageWrapper {
    height: 180px;
  }

  .overviewGrid {
    gap: 0.5rem;
  }

  .overviewItem {
    padding: 0.375rem 0;
  }

  .overviewLabel,
  .overviewValue {
    font-size: 0.8rem;
  }

  .sectionTitle {
    font-size: 0.9rem;
  }

  .specificationsList {
    gap: 0.375rem;
  }

  .specificationItem {
    padding: 0.375rem 0;
  }

  .specificationLabel,
  .specificationValue {
    font-size: 0.8rem;
  }
}
