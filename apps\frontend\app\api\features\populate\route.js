import { NextResponse } from 'next/server';
import { connectToDatabase } from '../../../../lib/dbConnect';
import Feature from '../../../../models/Feature';

const sampleFeatures = [
  // Exterior Features
  { category: 'exterior', value: 'Alloy Wheels' },
  { category: 'exterior', value: 'Sunroof' },
  { category: 'exterior', value: 'LED Headlights' },
  { category: 'exterior', value: 'Fog Lights' },
  { category: 'exterior', value: 'Roof Rails' },
  { category: 'exterior', value: 'Tinted Windows' },
  { category: 'exterior', value: 'Chrome Trim' },
  { category: 'exterior', value: 'Running Boards' },
  { category: 'exterior', value: 'Heated Mirrors' },
  { category: 'exterior', value: 'Power Mirrors' },

  // Interior Features
  { category: 'interior', value: 'Leather Seats' },
  { category: 'interior', value: 'Heated Seats' },
  { category: 'interior', value: 'Ventilated Seats' },
  { category: 'interior', value: 'Power Seats' },
  { category: 'interior', value: 'Memory Seats' },
  { category: 'interior', value: 'Dual Zone Climate Control' },
  { category: 'interior', value: 'Rear Air Conditioning' },
  { category: 'interior', value: 'Premium Sound System' },
  { category: 'interior', value: 'Navigation System' },
  { category: 'interior', value: 'Backup Camera' },
  { category: 'interior', value: '360 Degree Camera' },
  { category: 'interior', value: 'Wireless Charging' },
  { category: 'interior', value: 'USB Ports' },
  { category: 'interior', value: 'Cup Holders' },
  { category: 'interior', value: 'Storage Compartments' },

  // Mechanical Features
  { category: 'mechanical', value: 'All-Wheel Drive' },
  { category: 'mechanical', value: 'Four-Wheel Drive' },
  { category: 'mechanical', value: 'Turbo Engine' },
  { category: 'mechanical', value: 'Hybrid System' },
  { category: 'mechanical', value: 'Manual Transmission' },
  { category: 'mechanical', value: 'Automatic Transmission' },
  { category: 'mechanical', value: 'CVT Transmission' },
  { category: 'mechanical', value: 'Sport Mode' },
  { category: 'mechanical', value: 'Eco Mode' },
  { category: 'mechanical', value: 'Tow Package' },
  { category: 'mechanical', value: 'Limited Slip Differential' },
  { category: 'mechanical', value: 'Performance Suspension' },

  // Safety Features
  { category: 'safety', value: 'Anti-lock Brakes (ABS)' },
  { category: 'safety', value: 'Electronic Stability Control' },
  { category: 'safety', value: 'Traction Control' },
  { category: 'safety', value: 'Airbags' },
  { category: 'safety', value: 'Side Airbags' },
  { category: 'safety', value: 'Curtain Airbags' },
  { category: 'safety', value: 'Blind Spot Monitoring' },
  { category: 'safety', value: 'Lane Departure Warning' },
  { category: 'safety', value: 'Forward Collision Warning' },
  { category: 'safety', value: 'Automatic Emergency Braking' },
  { category: 'safety', value: 'Adaptive Cruise Control' },
  { category: 'safety', value: 'Parking Sensors' },
  { category: 'safety', value: 'Tire Pressure Monitoring' },
  { category: 'safety', value: 'Child Safety Locks' },

  // Entertainment Features
  { category: 'entertainment', value: 'Bluetooth Connectivity' },
  { category: 'entertainment', value: 'Apple CarPlay' },
  { category: 'entertainment', value: 'Android Auto' },
  { category: 'entertainment', value: 'Satellite Radio' },
  { category: 'entertainment', value: 'AM/FM Radio' },
  { category: 'entertainment', value: 'CD Player' },
  { category: 'entertainment', value: 'DVD Player' },
  { category: 'entertainment', value: 'Rear Entertainment System' },
  { category: 'entertainment', value: 'WiFi Hotspot' },
  { category: 'entertainment', value: 'Voice Control' },
  { category: 'entertainment', value: 'Steering Wheel Controls' },
  { category: 'entertainment', value: 'Touchscreen Display' },
];

/**
 * POST /api/features/populate - Populate sample features
 */
export async function POST(request) {
  try {
    await connectToDatabase();
    
    console.log('🌱 Starting to populate sample features...');
    
    // Clear existing features first (optional)
    const clearExisting = new URL(request.url).searchParams.get('clear') === 'true';
    if (clearExisting) {
      await Feature.deleteMany({});
      console.log('🗑️ Cleared existing features');
    }
    
    const results = [];
    let created = 0;
    let skipped = 0;
    
    for (const featureData of sampleFeatures) {
      try {
        // Check if feature already exists
        const existing = await Feature.findOne({ 
          category: featureData.category, 
          value: featureData.value 
        });
        
        if (existing) {
          skipped++;
          continue;
        }
        
        // Create new feature
        const feature = new Feature(featureData);
        await feature.save();
        results.push(feature);
        created++;
        
      } catch (error) {
        if (error.code === 11000) {
          // Duplicate key error - skip
          skipped++;
        } else {
          console.error('Error creating feature:', featureData, error);
        }
      }
    }
    
    console.log(`✅ Features populated: ${created} created, ${skipped} skipped`);
    
    return NextResponse.json({
      success: true,
      message: `Successfully populated features: ${created} created, ${skipped} skipped`,
      data: {
        created,
        skipped,
        total: sampleFeatures.length,
        features: results
      }
    });
    
  } catch (error) {
    console.error('Error populating features:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to populate features',
      details: error.message
    }, { status: 500 });
  }
}
