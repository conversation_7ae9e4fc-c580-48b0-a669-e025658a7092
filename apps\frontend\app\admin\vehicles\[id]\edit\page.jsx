// src/app/admin/vehicles/[id]/edit/page.jsx
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import FeaturesSelector from '../../../../../components/FeaturesSelector/FeaturesSelector';

export default function EditVehicle({ params }) {
  const { id } = params;
  const [vehicle, setVehicle] = useState(null);
  const [form, setForm] = useState({});
  const [selectedFeatures, setSelectedFeatures] = useState({
    exterior: [],
    interior: [],
    mechanical: [],
    safety: [],
    entertainment: []
  });
  const router = useRouter();

  useEffect(() => {
    fetch(`/api/vehicles/${id}`)
      .then(res => res.json())
      .then(data => {
        setVehicle(data.data);
        setForm(data.data);
        // Set features if they exist
        if (data.data.features) {
          setSelectedFeatures(data.data.features);
        }
      });
  }, [id]);

  const handleChange = e => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async e => {
    e.preventDefault();

    // Include features in the form data
    const submitData = {
      ...form,
      features: selectedFeatures
    };

    const res = await fetch(`/api/vehicles/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(submitData),
    });

    if (res.ok) {
      router.push('/admin'); // Redirect to dashboard
    }
  };

  if (!vehicle) return <p>Loading...</p>;

  return (
    <div className="edit-vehicle-container">
      <h2>Edit Vehicle</h2>
      <form onSubmit={handleSubmit}>
        {['vin', 'make', 'model', 'year', 'color', 'mileage', 'price'].map(field => (
          <div key={field} className="form-group">
            <label>{field.charAt(0).toUpperCase() + field.slice(1)}</label>
            <input
              type="text"
              name={field}
              value={form[field] || ''}
              onChange={handleChange}
              required
              className="formInput"
            />
          </div>
        ))}

        {/* Vehicle Features */}
        <div className="features-section">
          <h3>Vehicle Features</h3>
          <FeaturesSelector
            selectedFeatures={selectedFeatures}
            onFeaturesChange={setSelectedFeatures}
          />
        </div>

        <button type="submit" className="button-primary">Update</button>
      </form>
    </div>
  );
}
